#!/usr/bin/env python3
"""
Enhanced Connection Handler with retry logic, connection pooling, and robust error handling
"""

import time
import json
import threading
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import logging


class ConnectionState(Enum):
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    FAILED = "failed"
    UNKNOWN = "unknown"


@dataclass
class RetryConfig:
    max_retries: int = 3
    backoff_factor: float = 0.3
    status_forcelist: List[int] = None
    allowed_methods: List[str] = None
    
    def __post_init__(self):
        if self.status_forcelist is None:
            self.status_forcelist = [500, 502, 503, 504]
        if self.allowed_methods is None:
            self.allowed_methods = ["HEAD", "GET", "POST", "PUT", "DELETE", "OPTIONS", "TRACE"]


@dataclass
class TimeoutConfig:
    connect_timeout: float = 5.0
    read_timeout: float = 30.0
    chat_timeout: float = 120.0
    model_download_timeout: float = 300.0
    health_check_timeout: float = 10.0


class EnhancedOllamaAPI:
    """Enhanced Ollama API with connection pooling, retry logic, and health monitoring"""
    
    def __init__(self, base_url: str = "http://localhost:11434", config_file: str = "connection_config.json"):
        self.base_url = base_url
        self.config_file = config_file
        self.config = self._load_config()
        
        # Connection state management
        self.connection_state = ConnectionState.UNKNOWN
        self.last_health_check = 0
        self.health_check_interval = 30  # seconds
        self.consecutive_failures = 0
        self.max_consecutive_failures = 3
        
        # Setup session with connection pooling and retry logic
        self.session = self._create_session()
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Health monitoring thread
        self._start_health_monitor()
    
    def _load_config(self) -> Dict:
        """Load connection configuration"""
        default_config = {
            "retry": {
                "max_retries": 3,
                "backoff_factor": 0.3,
                "status_forcelist": [500, 502, 503, 504]
            },
            "timeouts": {
                "connect_timeout": 5.0,
                "read_timeout": 30.0,
                "chat_timeout": 120.0,
                "model_download_timeout": 300.0,
                "health_check_timeout": 10.0
            },
            "connection_pool": {
                "pool_connections": 10,
                "pool_maxsize": 20,
                "pool_block": False
            }
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    return {**default_config, **config}
        except Exception as e:
            self.logger.warning(f"Could not load config: {e}")
        
        return default_config
    
    def _create_session(self) -> requests.Session:
        """Create a session with connection pooling and retry logic"""
        session = requests.Session()
        
        # Configure retry strategy
        retry_config = RetryConfig(**self.config["retry"])
        retry_strategy = Retry(
            total=retry_config.max_retries,
            backoff_factor=retry_config.backoff_factor,
            status_forcelist=retry_config.status_forcelist,
            allowed_methods=retry_config.allowed_methods
        )
        
        # Configure connection pooling
        pool_config = self.config["connection_pool"]
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=pool_config["pool_connections"],
            pool_maxsize=pool_config["pool_maxsize"],
            pool_block=pool_config["pool_block"]
        )
        
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def _start_health_monitor(self):
        """Start background health monitoring"""
        def health_monitor():
            while True:
                try:
                    if time.time() - self.last_health_check > self.health_check_interval:
                        self._perform_health_check()
                    time.sleep(10)  # Check every 10 seconds
                except Exception as e:
                    self.logger.error(f"Health monitor error: {e}")
        
        monitor_thread = threading.Thread(target=health_monitor, daemon=True)
        monitor_thread.start()
    
    def _perform_health_check(self):
        """Perform health check on Ollama service"""
        try:
            timeout = self.config["timeouts"]["health_check_timeout"]
            response = self.session.get(f"{self.base_url}/api/tags", timeout=timeout)
            
            if response.status_code == 200:
                self.connection_state = ConnectionState.HEALTHY
                self.consecutive_failures = 0
            else:
                self._handle_health_check_failure(f"HTTP {response.status_code}")
                
            self.last_health_check = time.time()
            
        except Exception as e:
            self._handle_health_check_failure(str(e))
    
    def _handle_health_check_failure(self, error: str):
        """Handle health check failure"""
        self.consecutive_failures += 1
        
        if self.consecutive_failures >= self.max_consecutive_failures:
            self.connection_state = ConnectionState.FAILED
        else:
            self.connection_state = ConnectionState.DEGRADED
        
        self.logger.warning(f"Health check failed: {error} (failures: {self.consecutive_failures})")
    
    def list_models(self) -> List[str]:
        """Get list of available models with enhanced error handling"""
        try:
            if self.connection_state == ConnectionState.FAILED:
                self.logger.warning("Connection is in failed state, attempting anyway...")
            
            timeout = (
                self.config["timeouts"]["connect_timeout"],
                self.config["timeouts"]["read_timeout"]
            )
            
            response = self.session.get(f"{self.base_url}/api/tags", timeout=timeout)
            
            if response.status_code == 200:
                data = response.json()
                models = [model['name'] for model in data.get('models', [])]
                self.logger.info(f"Successfully retrieved {len(models)} models")
                return models
            else:
                self.logger.error(f"Failed to list models: HTTP {response.status_code}")
                return []
                
        except requests.exceptions.Timeout:
            self.logger.error("Timeout while listing models")
            return []
        except requests.exceptions.ConnectionError:
            self.logger.error("Connection error while listing models")
            return []
        except Exception as e:
            self.logger.error(f"Unexpected error listing models: {e}")
            return []
    
    def chat(self, model: str, messages: List[Dict], stream: bool = True, operation_timeout: float = None):
        """Send chat request with enhanced error handling and timeout management"""
        try:
            if self.connection_state == ConnectionState.FAILED:
                raise ConnectionError("Service is currently unavailable")
            
            payload = {
                "model": model,
                "messages": messages,
                "stream": stream
            }
            
            # Use operation-specific timeout or default
            if operation_timeout is None:
                operation_timeout = self.config["timeouts"]["chat_timeout"]
            
            timeout = (
                self.config["timeouts"]["connect_timeout"],
                operation_timeout
            )
            
            response = self.session.post(
                f"{self.base_url}/api/chat",
                json=payload,
                stream=stream,
                timeout=timeout
            )
            
            if response.status_code == 200:
                self.logger.info(f"Chat request successful for model: {model}")
                return response
            else:
                error_msg = f"Chat request failed: HTTP {response.status_code}"
                self.logger.error(error_msg)
                raise requests.exceptions.HTTPError(error_msg)
                
        except requests.exceptions.Timeout as e:
            self.logger.error(f"Chat request timed out: {e}")
            raise
        except requests.exceptions.ConnectionError as e:
            self.logger.error(f"Connection error during chat: {e}")
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error during chat: {e}")
            raise
    
    def pull_model(self, model_name: str, progress_callback: Callable = None):
        """Pull/download a model with progress tracking"""
        try:
            payload = {"name": model_name}
            timeout = (
                self.config["timeouts"]["connect_timeout"],
                self.config["timeouts"]["model_download_timeout"]
            )
            
            response = self.session.post(
                f"{self.base_url}/api/pull",
                json=payload,
                stream=True,
                timeout=timeout
            )
            
            if response.status_code == 200:
                self.logger.info(f"Started pulling model: {model_name}")
                
                # Process streaming response for progress updates
                for line in response.iter_lines():
                    if line:
                        try:
                            data = json.loads(line.decode('utf-8'))
                            if progress_callback:
                                progress_callback(data)
                        except json.JSONDecodeError:
                            continue
                
                return response
            else:
                error_msg = f"Model pull failed: HTTP {response.status_code}"
                self.logger.error(error_msg)
                raise requests.exceptions.HTTPError(error_msg)
                
        except requests.exceptions.Timeout as e:
            self.logger.error(f"Model pull timed out: {e}")
            raise
        except requests.exceptions.ConnectionError as e:
            self.logger.error(f"Connection error during model pull: {e}")
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error during model pull: {e}")
            raise
    
    def get_connection_status(self) -> Dict:
        """Get current connection status and statistics"""
        return {
            "state": self.connection_state.value,
            "consecutive_failures": self.consecutive_failures,
            "last_health_check": self.last_health_check,
            "base_url": self.base_url,
            "session_active": self.session is not None
        }
    
    def reset_connection(self):
        """Reset connection and clear failure state"""
        self.logger.info("Resetting connection...")
        self.consecutive_failures = 0
        self.connection_state = ConnectionState.UNKNOWN
        self.session.close()
        self.session = self._create_session()
        self._perform_health_check()
    
    def close(self):
        """Clean up resources"""
        if self.session:
            self.session.close()
            self.logger.info("Connection closed")
