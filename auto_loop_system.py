#!/usr/bin/env python3
"""
Auto-Loop Switch System for automatic retry of failed operations
"""

import time
import threading
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Callable, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QCheckBox, QSpinBox, QComboBox, QTextEdit, QGroupBox


class OperationMode(Enum):
    MANUAL = "manual"
    AUTO = "auto"
    SMART_AUTO = "smart_auto"  # Auto with intelligent backoff


class OperationStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    RETRYING = "retrying"
    CANCELLED = "cancelled"


@dataclass
class RetryOperation:
    id: str
    name: str
    function: Callable
    args: tuple
    kwargs: dict
    max_retries: int
    current_retry: int
    status: OperationStatus
    created_at: datetime
    last_attempt: Optional[datetime]
    next_attempt: Optional[datetime]
    error_message: Optional[str]
    backoff_multiplier: float = 1.5
    base_delay: float = 1.0


class AutoLoopSystem(QObject):
    """Auto-loop system for automatic retry of failed operations"""

    # Signals for GUI updates
    operation_started = pyqtSignal(str)  # operation_id
    operation_completed = pyqtSignal(str, bool)  # operation_id, success
    operation_failed = pyqtSignal(str, str)  # operation_id, error_message
    status_updated = pyqtSignal(str)  # status_message

    def __init__(self, config_file: str = "auto_loop_config.json"):
        super().__init__()
        self.config_file = config_file
        self.config = self._load_config()

        # Operation management
        self.operations: Dict[str, RetryOperation] = {}
        self.operation_mode = OperationMode(self.config.get("mode", "manual"))
        self.is_running = False
        self.worker_thread = None

        # Statistics
        self.stats = {
            "total_operations": 0,
            "successful_operations": 0,
            "failed_operations": 0,
            "retries_performed": 0,
            "uptime_start": datetime.now()
        }

        # Setup logging
        self.logger = logging.getLogger(__name__)

        # GUI timer for updates
        self.gui_update_timer = QTimer()
        self.gui_update_timer.timeout.connect(self._emit_status_update)
        self.gui_update_timer.start(1000)  # Update every second

    def _load_config(self) -> Dict:
        """Load auto-loop configuration"""
        default_config = {
            "mode": "manual",
            "max_retries": 3,
            "base_delay": 1.0,
            "backoff_multiplier": 1.5,
            "max_delay": 60.0,
            "operation_timeout": 300.0,
            "cleanup_interval": 3600,  # 1 hour
            "max_operation_history": 100
        }

        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    return {**default_config, **config}
        except Exception as e:
            self.logger.warning(f"Could not load config: {e}")

        return default_config

    def _save_config(self):
        """Save current configuration"""
        try:
            config_to_save = {**self.config, "mode": self.operation_mode.value}
            with open(self.config_file, 'w') as f:
                json.dump(config_to_save, f, indent=2)
        except Exception as e:
            self.logger.error(f"Could not save config: {e}")

    def set_operation_mode(self, mode: OperationMode):
        """Set the operation mode (manual/auto/smart_auto)"""
        self.operation_mode = mode
        self.config["mode"] = mode.value
        self._save_config()

        if mode != OperationMode.MANUAL and not self.is_running:
            self.start_auto_loop()
        elif mode == OperationMode.MANUAL and self.is_running:
            self.stop_auto_loop()

        self.status_updated.emit(f"Operation mode set to: {mode.value}")

    def add_operation(self, operation_id: str, name: str, function: Callable,
                     *args, max_retries: int = None, **kwargs) -> str:
        """Add an operation to the retry queue"""
        if max_retries is None:
            max_retries = self.config["max_retries"]

        operation = RetryOperation(
            id=operation_id,
            name=name,
            function=function,
            args=args,
            kwargs=kwargs,
            max_retries=max_retries,
            current_retry=0,
            status=OperationStatus.PENDING,
            created_at=datetime.now(),
            last_attempt=None,
            next_attempt=datetime.now(),
            error_message=None,
            backoff_multiplier=self.config["backoff_multiplier"],
            base_delay=self.config["base_delay"]
        )

        self.operations[operation_id] = operation
        self.stats["total_operations"] += 1

        self.logger.info(f"Added operation: {name} (ID: {operation_id})")

        # If in auto mode, start processing immediately
        if self.operation_mode != OperationMode.MANUAL and not self.is_running:
            self.start_auto_loop()

        return operation_id

    def execute_operation(self, operation_id: str) -> bool:
        """Execute a single operation"""
        if operation_id not in self.operations:
            self.logger.error(f"Operation not found: {operation_id}")
            return False

        operation = self.operations[operation_id]
        operation.status = OperationStatus.RUNNING
        operation.last_attempt = datetime.now()

        self.operation_started.emit(operation_id)

        try:
            # Execute the operation
            result = operation.function(*operation.args, **operation.kwargs)

            # Consider operation successful if it doesn't raise an exception
            # and returns a truthy value (or None for void functions)
            if result is not False:
                operation.status = OperationStatus.SUCCESS
                self.stats["successful_operations"] += 1
                self.operation_completed.emit(operation_id, True)
                self.logger.info(f"Operation completed successfully: {operation.name}")
                return True
            else:
                raise Exception("Operation returned False")

        except Exception as e:
            operation.current_retry += 1
            operation.error_message = str(e)
            self.stats["retries_performed"] += 1

            if operation.current_retry >= operation.max_retries:
                operation.status = OperationStatus.FAILED
                self.stats["failed_operations"] += 1
                self.operation_failed.emit(operation_id, str(e))
                self.logger.error(f"Operation failed permanently: {operation.name} - {e}")
                return False
            else:
                operation.status = OperationStatus.RETRYING

                # Calculate next retry time with exponential backoff
                delay = operation.base_delay * (operation.backoff_multiplier ** (operation.current_retry - 1))
                delay = min(delay, self.config["max_delay"])

                operation.next_attempt = datetime.now() + timedelta(seconds=delay)

                self.logger.warning(f"Operation failed, will retry in {delay:.1f}s: {operation.name} - {e}")
                return False

    def start_auto_loop(self):
        """Start the auto-loop worker thread"""
        if self.is_running:
            return

        self.is_running = True
        self.worker_thread = threading.Thread(target=self._auto_loop_worker, daemon=True)
        self.worker_thread.start()

        self.status_updated.emit("Auto-loop started")
        self.logger.info("Auto-loop system started")

    def stop_auto_loop(self):
        """Stop the auto-loop worker thread"""
        self.is_running = False
        if self.worker_thread:
            self.worker_thread.join(timeout=5)

        self.status_updated.emit("Auto-loop stopped")
        self.logger.info("Auto-loop system stopped")

    def _auto_loop_worker(self):
        """Main worker thread for auto-loop processing"""
        while self.is_running:
            try:
                current_time = datetime.now()
                operations_to_process = []

                # Find operations ready for execution
                for op_id, operation in self.operations.items():
                    if (operation.status in [OperationStatus.PENDING, OperationStatus.RETRYING] and
                        operation.next_attempt <= current_time):
                        operations_to_process.append(op_id)

                # Execute ready operations
                for op_id in operations_to_process:
                    if not self.is_running:
                        break

                    self.execute_operation(op_id)

                    # Small delay between operations to prevent overwhelming
                    time.sleep(0.1)

                # Cleanup old completed operations
                self._cleanup_old_operations()

                # Sleep before next iteration
                time.sleep(1)

            except Exception as e:
                self.logger.error(f"Auto-loop worker error: {e}")
                time.sleep(5)  # Longer sleep on error

    def _cleanup_old_operations(self):
        """Clean up old completed operations"""
        try:
            current_time = datetime.now()
            cleanup_threshold = current_time - timedelta(seconds=self.config["cleanup_interval"])

            operations_to_remove = []
            for op_id, operation in self.operations.items():
                if (operation.status in [OperationStatus.SUCCESS, OperationStatus.FAILED] and
                    operation.last_attempt and operation.last_attempt < cleanup_threshold):
                    operations_to_remove.append(op_id)

            # Keep only the most recent operations
            if len(self.operations) > self.config["max_operation_history"]:
                sorted_ops = sorted(self.operations.items(),
                                  key=lambda x: x[1].created_at, reverse=True)
                operations_to_remove.extend([op_id for op_id, _ in sorted_ops[self.config["max_operation_history"]:]])

            for op_id in operations_to_remove:
                del self.operations[op_id]

            if operations_to_remove:
                self.logger.info(f"Cleaned up {len(operations_to_remove)} old operations")

        except Exception as e:
            self.logger.error(f"Cleanup error: {e}")

    def _emit_status_update(self):
        """Emit status update for GUI"""
        if not self.operations:
            return

        pending = sum(1 for op in self.operations.values() if op.status == OperationStatus.PENDING)
        running = sum(1 for op in self.operations.values() if op.status == OperationStatus.RUNNING)
        retrying = sum(1 for op in self.operations.values() if op.status == OperationStatus.RETRYING)

        if running > 0 or retrying > 0 or pending > 0:
            status = f"Operations - Running: {running}, Pending: {pending}, Retrying: {retrying}"
            self.status_updated.emit(status)

    def get_operation_status(self, operation_id: str) -> Optional[Dict]:
        """Get status of a specific operation"""
        if operation_id not in self.operations:
            return None

        operation = self.operations[operation_id]
        return {
            "id": operation.id,
            "name": operation.name,
            "status": operation.status.value,
            "current_retry": operation.current_retry,
            "max_retries": operation.max_retries,
            "created_at": operation.created_at.isoformat(),
            "last_attempt": operation.last_attempt.isoformat() if operation.last_attempt else None,
            "next_attempt": operation.next_attempt.isoformat() if operation.next_attempt else None,
            "error_message": operation.error_message
        }

    def get_all_operations(self) -> List[Dict]:
        """Get status of all operations"""
        return [self.get_operation_status(op_id) for op_id in self.operations.keys()]

    def get_statistics(self) -> Dict:
        """Get system statistics"""
        uptime = datetime.now() - self.stats["uptime_start"]
        return {
            **self.stats,
            "uptime_seconds": uptime.total_seconds(),
            "active_operations": len(self.operations),
            "operation_mode": self.operation_mode.value,
            "is_running": self.is_running
        }

    def cancel_operation(self, operation_id: str) -> bool:
        """Cancel a pending or retrying operation"""
        if operation_id not in self.operations:
            return False

        operation = self.operations[operation_id]
        if operation.status in [OperationStatus.PENDING, OperationStatus.RETRYING]:
            operation.status = OperationStatus.CANCELLED
            self.logger.info(f"Operation cancelled: {operation.name}")
            return True

        return False

    def retry_failed_operation(self, operation_id: str) -> bool:
        """Manually retry a failed operation"""
        if operation_id not in self.operations:
            return False

        operation = self.operations[operation_id]
        if operation.status == OperationStatus.FAILED:
            operation.status = OperationStatus.PENDING
            operation.current_retry = 0
            operation.next_attempt = datetime.now()
            operation.error_message = None
            self.logger.info(f"Operation reset for retry: {operation.name}")
            return True

        return False


class AutoLoopControlWidget(QWidget):
    """GUI widget for controlling the auto-loop system"""

    def __init__(self, auto_loop_system: AutoLoopSystem):
        super().__init__()
        self.auto_loop = auto_loop_system
        self.init_ui()
        self.connect_signals()

    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout(self)

        # Control group
        control_group = QGroupBox("Auto-Loop Control")
        control_layout = QVBoxLayout(control_group)

        # Mode selection
        mode_layout = QHBoxLayout()
        mode_layout.addWidget(QLabel("Operation Mode:"))
        self.mode_combo = QComboBox()
        self.mode_combo.addItems(["Manual", "Auto", "Smart Auto"])
        self.mode_combo.currentTextChanged.connect(self.on_mode_changed)
        mode_layout.addWidget(self.mode_combo)
        mode_layout.addStretch()
        control_layout.addLayout(mode_layout)

        # Control buttons
        button_layout = QHBoxLayout()
        self.start_btn = QPushButton("Start Auto-Loop")
        self.start_btn.clicked.connect(self.start_auto_loop)
        button_layout.addWidget(self.start_btn)

        self.stop_btn = QPushButton("Stop Auto-Loop")
        self.stop_btn.clicked.connect(self.stop_auto_loop)
        button_layout.addWidget(self.stop_btn)

        self.clear_btn = QPushButton("Clear Completed")
        self.clear_btn.clicked.connect(self.clear_completed_operations)
        button_layout.addWidget(self.clear_btn)

        button_layout.addStretch()
        control_layout.addLayout(button_layout)

        layout.addWidget(control_group)

        # Configuration group
        config_group = QGroupBox("Configuration")
        config_layout = QVBoxLayout(config_group)

        # Max retries
        retries_layout = QHBoxLayout()
        retries_layout.addWidget(QLabel("Max Retries:"))
        self.max_retries_spin = QSpinBox()
        self.max_retries_spin.setRange(1, 10)
        self.max_retries_spin.setValue(self.auto_loop.config["max_retries"])
        self.max_retries_spin.valueChanged.connect(self.on_config_changed)
        retries_layout.addWidget(self.max_retries_spin)
        retries_layout.addStretch()
        config_layout.addLayout(retries_layout)

        # Base delay
        delay_layout = QHBoxLayout()
        delay_layout.addWidget(QLabel("Base Delay (seconds):"))
        self.base_delay_spin = QSpinBox()
        self.base_delay_spin.setRange(1, 60)
        self.base_delay_spin.setValue(int(self.auto_loop.config["base_delay"]))
        self.base_delay_spin.valueChanged.connect(self.on_config_changed)
        delay_layout.addWidget(self.base_delay_spin)
        delay_layout.addStretch()
        config_layout.addLayout(delay_layout)

        layout.addWidget(config_group)

        # Status group
        status_group = QGroupBox("Status & Operations")
        status_layout = QVBoxLayout(status_group)

        # Status label
        self.status_label = QLabel("Ready")
        status_layout.addWidget(self.status_label)

        # Operations display
        self.operations_display = QTextEdit()
        self.operations_display.setMaximumHeight(200)
        self.operations_display.setReadOnly(True)
        status_layout.addWidget(self.operations_display)

        layout.addWidget(status_group)

        # Update display
        self.update_display()

    def connect_signals(self):
        """Connect auto-loop system signals"""
        self.auto_loop.operation_started.connect(self.on_operation_started)
        self.auto_loop.operation_completed.connect(self.on_operation_completed)
        self.auto_loop.operation_failed.connect(self.on_operation_failed)
        self.auto_loop.status_updated.connect(self.on_status_updated)

    def on_mode_changed(self, mode_text: str):
        """Handle mode change"""
        mode_map = {
            "Manual": OperationMode.MANUAL,
            "Auto": OperationMode.AUTO,
            "Smart Auto": OperationMode.SMART_AUTO
        }

        if mode_text in mode_map:
            self.auto_loop.set_operation_mode(mode_map[mode_text])
            self.update_display()

    def on_config_changed(self):
        """Handle configuration changes"""
        self.auto_loop.config["max_retries"] = self.max_retries_spin.value()
        self.auto_loop.config["base_delay"] = float(self.base_delay_spin.value())
        self.auto_loop._save_config()

    def start_auto_loop(self):
        """Start the auto-loop system"""
        self.auto_loop.start_auto_loop()
        self.update_display()

    def stop_auto_loop(self):
        """Stop the auto-loop system"""
        self.auto_loop.stop_auto_loop()
        self.update_display()

    def clear_completed_operations(self):
        """Clear completed operations"""
        operations_to_remove = []
        for op_id, operation in self.auto_loop.operations.items():
            if operation.status in [OperationStatus.SUCCESS, OperationStatus.FAILED]:
                operations_to_remove.append(op_id)

        for op_id in operations_to_remove:
            del self.auto_loop.operations[op_id]

        self.update_display()

    def update_display(self):
        """Update the display with current status"""
        # Update mode combo
        mode_text_map = {
            OperationMode.MANUAL: "Manual",
            OperationMode.AUTO: "Auto",
            OperationMode.SMART_AUTO: "Smart Auto"
        }

        current_mode_text = mode_text_map.get(self.auto_loop.operation_mode, "Manual")
        self.mode_combo.setCurrentText(current_mode_text)

        # Update button states
        self.start_btn.setEnabled(not self.auto_loop.is_running)
        self.stop_btn.setEnabled(self.auto_loop.is_running)

        # Update operations display
        self.update_operations_display()

    def update_operations_display(self):
        """Update the operations display"""
        operations = self.auto_loop.get_all_operations()

        if not operations:
            self.operations_display.setText("No operations")
            return

        display_text = ""
        for op in operations[-10:]:  # Show last 10 operations
            status_icon = {
                "pending": "⏳",
                "running": "🔄",
                "success": "✅",
                "failed": "❌",
                "retrying": "🔁",
                "cancelled": "🚫"
            }.get(op["status"], "❓")

            display_text += f"{status_icon} {op['name']} "
            display_text += f"({op['current_retry']}/{op['max_retries']})\n"

            if op["error_message"]:
                display_text += f"   Error: {op['error_message'][:50]}...\n"

            display_text += "\n"

        self.operations_display.setText(display_text)

    def on_operation_started(self, operation_id: str):
        """Handle operation started signal"""
        self.update_operations_display()

    def on_operation_completed(self, operation_id: str, success: bool):
        """Handle operation completed signal"""
        self.update_operations_display()

    def on_operation_failed(self, operation_id: str, error_message: str):
        """Handle operation failed signal"""
        self.update_operations_display()

    def on_status_updated(self, status_message: str):
        """Handle status update signal"""
        self.status_label.setText(status_message)
