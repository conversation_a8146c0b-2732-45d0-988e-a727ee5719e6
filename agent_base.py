import time
from error_logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from nexus_light import <PERSON>exus<PERSON><PERSON>
from rag_database import RAGDatabase # Import the RAGDatabase class
from context_7_mcp_server import Context7MCPServer # Import the Context7MCPServer class

class AgentBase:
    """
    Base class for all agents in the AI Agent Army.
    """
    def __init__(self, name: str, orchestrator, is_sub_agent: bool = False, parent_agent_name: str = None):
        self.name = name
        self.orchestrator = orchestrator # The orchestrator instance managing this agent
        self.status = "initialized" # e.g., initialized, idle, working, error
        self.is_sub_agent = is_sub_agent
        self.parent_agent_name = parent_agent_name # New attribute to track parent
        self.sub_agents = [] # List to track direct sub-agents created by this agent
        self.start_time = time.time() # To track individual agent running time

        if self.is_sub_agent:
            self.rag_database = None
            self.context_7_mcp_server = Context7MCPServer() # Initialize Context7MCPServer instance
        else:
            self.rag_database = RAGDatabase() # Initialize RAG database instance
            self.context_7_mcp_server = None
        self.system_prompt = (
            "You are an AI agent in the AI Agent Army. "
            "Your goal is to assist in complex tasks, learn from interactions, "
            "and utilize your tools and RAG database effectively."
        ) # Initialize system prompt
        self.is_active = True # Kill switch mechanism (True means running, False means paused/stopped)
        self._is_killed = False # Internal flag for permanent termination

        # Initialize ErrorLogger and NexusLight, passing the orchestrator reference
        self.error_logger = ErrorLogger(orchestrator_ref=self.orchestrator)
        # Pass parent_agent_name to NexusLight for hierarchy tracking
        self.nexus_light = NexusLight(agent_name=self.name, orchestrator_ref=self.orchestrator, parent_agent_name=self.parent_agent_name)

    def set_status(self, status: str):
        """Set the current status of the agent."""
        self.status = status
        print(f"Agent {self.name} status changed to: {self.status}") # Basic logging

    def stop(self):
        """Pause the agent's operation gracefully."""
        if self.is_active:
            self.is_active = False
            self.set_status("paused")
            print(f"Agent {self.name} has been paused.")
            if self.orchestrator and self.orchestrator.gui_ref:
                self.orchestrator.gui_ref.update_kill_switch_status(self.name, False)

    def kill(self):
        """Activate the kill switch for the agent, leading to permanent termination."""
        self.stop() # First, pause the agent
        self._is_killed = True
        self.set_status("killed")
        print(f"Agent {self.name} has been permanently killed.")

    def update_system_prompt(self, new_prompt: str):
        """Updates the agent's system prompt."""
        self.system_prompt = new_prompt
        print(f"Agent {self.name} system prompt updated.")

    def run(self):
        """
        Main execution loop for the agent.
        This method should be overridden by subclasses.
        """
        self.set_status("running")
        print(f"Agent {self.name} started with system prompt: {self.system_prompt}")
        if self.orchestrator and self.orchestrator.gui_ref:
            self.orchestrator.gui_ref.append_terminal_output(f"Agent {self.name} started. Status: {self.status}", output_type="Narrative")

        # Demonstrate basic context interaction based on agent type
        if self.rag_database:
            self.rag_database.add_document("initial_context", f"This is initial context for {self.name}.")
            retrieved = self.rag_database.retrieve_information("context")
            print(f"Agent {self.name} retrieved from RAG: {retrieved}")
            if self.orchestrator and self.orchestrator.gui_ref:
                self.orchestrator.gui_ref.append_terminal_output(f"Narrative: {self.name} retrieved context from RAG: '{retrieved}'", output_type="Narrative")
        elif self.context_7_mcp_server:
            # Demonstrate basic context interaction for sub-agents
            self.context_7_mcp_server.update_context(self.name, f"Sub-agent {self.name} is starting its task.")
            retrieved_context = self.context_7_mcp_server.get_context(self.name, "starting task")
            print(f"Agent {self.name} retrieved from Context 7 MCP Server: {retrieved_context}")
            if self.orchestrator and self.orchestrator.gui_ref:
                self.orchestrator.gui_ref.append_terminal_output(f"Narrative: {self.name} retrieved context from Context 7 MCP Server: '{retrieved_context}'", output_type="Narrative")

        status_update_interval = 5 # seconds
        last_status_update_time = time.time()
        performance_report_interval = 30 # seconds (report performance every 30 seconds)
        last_performance_report_time = time.time()

        while self.is_active and not self._is_killed:
            try:
                self.set_status("idle")
                self.set_status("idle")
                print(f"Agent {self.name} is idle, querying Orchestrator for tasks...")
                if self.orchestrator and self.orchestrator.gui_ref:
                    self.orchestrator.gui_ref.append_terminal_output(f"Narrative: {self.name} is idle and querying Orchestrator for tasks.", output_type="Narrative")
                
                # Query the Orchestrator for a task
                response = self.nexus_light.query_orchestrator_for_directives()
                
                if response and response.get("type") == "task_assigned":
                    task = response.get("task")
                    self.current_task = task # Store the assigned task
                    self.set_status("working")
                    print(f"Agent {self.name} received task: {task['description']} (ID: {task['task_id']})")
                    if self.orchestrator and self.orchestrator.gui_ref:
                        self.orchestrator.gui_ref.update_chat_display(self.name, f"Received task: {task['description']}")
                        self.orchestrator.gui_ref.append_terminal_output(f"Narrative: {self.name} received task: '{task['description']}'.", output_type="Narrative")
                        # Simulate a code snippet explanation
                        simulated_code = f"def perform_task_{task['task_id'][:4]}():\n    # Complex logic for '{task['description']}'\n    result = 'simulated_result'\n    return result"
                        self.orchestrator.gui_ref.append_terminal_output(simulated_code, output_type="Code Snippet")
                        self.orchestrator.gui_ref.append_terminal_output(f"Explanation: The agent is now executing a simulated function to complete the task '{task['description']}'.", output_type="Narrative")
                    # Periodically report status while working
                    work_duration = random.randint(5, 15) # Simulate variable work time
                    start_work_time = time.time()
                    while (time.time() - start_work_time) < work_duration and self.is_active and not self._is_killed:
                        if (time.time() - last_status_update_time) >= status_update_interval:
                            self.report_status_to_gui()
                            last_status_update_time = time.time()
                        time.sleep(1) # Small sleep to allow other operations

                    # Simulate working on the task
                    # Report task completion
                    self.nexus_light.send_message(
                        recipient_agent_name="Orchestrator",
                        message_payload={
                            "type": "task_completed",
                            "task_id": task["task_id"],
                            "status": "completed",
                            "content": f"Task '{task['description']}' completed."
                        }
                    )
                    self.set_status("completed_task")
                    print(f"Agent {self.name} completed task: {task['task_id']}")
                    self.current_task = None # Clear the current task
                elif response and response.get("type") == "no_task_available":
                    print(f"Agent {self.name}: {response.get('content')}")
                    self.set_status("idle")
                    if self.orchestrator and self.orchestrator.gui_ref:
                        self.orchestrator.gui_ref.append_terminal_output(f"Narrative: {self.name} found no tasks available and will wait.", output_type="Narrative")
                    if (time.time() - last_status_update_time) >= status_update_interval:
                        self.report_status_to_gui()
                        last_status_update_time = time.time()
                    time.sleep(5) # Wait before querying again
                else:
                    print(f"Agent {self.name}: No valid response from Orchestrator or no task assigned. Waiting...")
                    self.set_status("idle")
                    if self.orchestrator and self.orchestrator.gui_ref:
                        self.orchestrator.gui_ref.append_terminal_output(f"Narrative: {self.name} received an invalid response or no task. Waiting.", output_type="Narrative")
                    if (time.time() - last_status_update_time) >= status_update_interval:
                        self.report_status_to_gui()
                        last_status_update_time = time.time()
                    time.sleep(5) # Wait before querying again

                # Ensure status is reported at least once per idle cycle
                if (time.time() - last_status_update_time) >= status_update_interval:
                    self.report_status_to_gui()
                    last_status_update_time = time.time()

                # Periodically report performance data to Orchestrator
                if (time.time() - last_performance_report_time) >= performance_report_interval:
                    # Simulate performance data
                    simulated_performance_data = {
                        "system_cpu": f"{random.uniform(5.0, 95.0):.2f}%",
                        "system_memory": f"{random.uniform(100.0, 4000.0):.2f}MB",
                        "network_latency": f"{random.uniform(10.0, 500.0):.2f}ms",
                        "disk_io": f"{random.uniform(0.1, 50.0):.2f}MB/s",
                        "agent_cpu": f"{random.uniform(1.0, 20.0):.2f}%",
                        "agent_memory": f"{random.uniform(10.0, 500.0):.2f}MB",
                        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
                    }

                    # Report performance data to Orchestrator
                    if self.orchestrator:
                        self.orchestrator.receive_performance_report(self.name, simulated_performance_data)

                    last_performance_report_time = time.time()

            except Exception as e:
                error_message = f"Unhandled exception in agent {self.name}: {e}"
                details = {"exception_type": type(e).__name__, "traceback": str(e)}
                self.error_logger.log_error(
                    agent_name=self.name,
                    error_message=error_message,
                    details=details
                )
                self.set_status("error")
                if self.orchestrator and self.orchestrator.gui_ref:
                    self.orchestrator.gui_ref.append_terminal_output(f"Agent {self.name} encountered an error: {error_message}", output_type="Error")
                # Optionally, you might want to stop the agent or attempt a restart here
                if (time.time() - last_status_update_time) >= status_update_interval:
                    self.report_status_to_gui()
                    last_status_update_time = time.time()
                time.sleep(5) # Wait before retrying or stopping
        # Agent loop exited, set final status
        if self._is_killed:
            self.set_status("killed")
            print(f"Agent {self.name} terminated due to kill switch.")
            if self.orchestrator and self.orchestrator.gui_ref:
                self.orchestrator.gui_ref.append_terminal_output(f"Agent {self.name} terminated due to kill switch.", output_type="System")
        elif not self.is_active:
            self.set_status("stopped") # Or "paused" if it was just stopped
            print(f"Agent {self.name} gracefully stopped/paused.")
            if self.orchestrator and self.orchestrator.gui_ref:
                self.orchestrator.gui_ref.append_terminal_output(f"Agent {self.name} gracefully stopped/paused.", output_type="System")

    def report_status_to_gui(self):
        """
        Placeholder method to send agent status (running, CPU, memory) to the GUI.
        In a real implementation, this would gather actual metrics.
        """
        if self.orchestrator and self.orchestrator.gui_ref:
            # Placeholder for actual metrics
            # Simulate running time
            elapsed_time = int(time.time() - self.start_time)
            hours, remainder = divmod(elapsed_time, 3600)
            minutes, seconds = divmod(remainder, 60)
            running_time = f"{hours:02}h {minutes:02}m {seconds:02}s"

            # Simulate CPU usage (0-100%)
            cpu_usage = f"{random.uniform(0.0, 50.0):.2f}%"
            # Simulate Memory usage (MB)
            memory_usage = f"{random.uniform(50.0, 500.0):.2f}MB"
            # Simulate Network usage (KB/s)
            network_usage = f"In {random.uniform(10.0, 100.0):.2f}KB/s / Out {random.uniform(5.0, 50.0):.2f}KB/s"

            self.orchestrator.gui_ref.update_agent_status(
                self.name,
                running_time=running_time,
                cpu_usage=cpu_usage,
                memory_usage=memory_usage,
                network_usage=network_usage # Pass simulated network usage
            )

    def add_sub_agent(self, sub_agent_name: str):
        """
        Adds a sub-agent to this agent's direct sub-agent list.
        """
        if sub_agent_name not in self.sub_agents:
            self.sub_agents.append(sub_agent_name)
            print(f"Agent {self.name}: Added sub-agent {sub_agent_name}. Current sub-agents: {self.sub_agents}")