#!/usr/bin/env python3
"""
Comprehensive Test Suite for Enhanced Self-UpgradingAI System
"""

import unittest
import tempfile
import shutil
import os
import json
import time
import sys
import random
import uuid
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta

# Import enhanced components for testing
from enhanced_config_manager import EnhancedConfigManager, OllamaConfig, UIConfig
from enhanced_connection_handler import EnhancedOllamaAPI, ConnectionState
from enhanced_error_handling import EnhancedErrorHandler, ErrorCategory, ErrorSeverity
from auto_loop_system import AutoLoopSystem, OperationMode, OperationStatus
from improved_self_upgrade_system import EnhancedSelfUpgradeSystem

# Import core system components for testing
from orchestrator import Orchestrator
from agent_base import AgentBase
from error_logger import ErrorLogger
from nexus_light import NexusLight
from rag_database import RAGDatabase
from context_7_mcp_server import Context7MCPServer

# Import specific agent types for Orchestrator's create_agent method
from search_agent import SearchAgent
from programming_agent import ProgrammingAgent
from debugging_optimization_agent import DebuggingOptimizationAgent
from communications_agent import CommunicationsAgent
from self_upgrading_agent import SelfUpgradingAgent
from lovelo_agent import LoveloAgent
from upload_agent import UploadAgent
from sentinel_agent import SentinelAgent
from hound_agent import HoundAgent
from pup_agent import PupAgent
from reconnaissance_agent import ReconnaissanceAgent


class TestEnhancedConfigManager(unittest.TestCase):
    """Test enhanced configuration management"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.config_manager = EnhancedConfigManager(self.temp_dir)
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir)
    
    def test_default_configuration(self):
        """Test default configuration values"""
        self.assertEqual(self.config_manager.ollama.base_url, "http://localhost:11434")
        self.assertEqual(self.config_manager.ui.window_width, 1200)
        self.assertEqual(self.config_manager.auto_loop.mode, "manual")
    
    def test_configuration_persistence(self):
        """Test configuration saving and loading"""
        # Modify configuration
        self.config_manager.ollama.base_url = "http://test:11434"
        self.config_manager.ui.window_width = 1600
        
        # Save configuration
        self.config_manager.save_all_configs()
        
        # Create new instance and verify persistence
        new_config_manager = EnhancedConfigManager(self.temp_dir)
        self.assertEqual(new_config_manager.ollama.base_url, "http://test:11434")
        self.assertEqual(new_config_manager.ui.window_width, 1600)
    
    def test_configuration_validation(self):
        """Test configuration validation"""
        # Set invalid values
        self.config_manager.ollama.base_url = "invalid-url"
        self.config_manager.ui.window_width = 100
        
        issues = self.config_manager.validate_config()
        self.assertGreater(len(issues), 0)
        self.assertTrue(any("base URL" in issue for issue in issues))
        self.assertTrue(any("width" in issue for issue in issues))
    
    def test_environment_variable_override(self):
        """Test environment variable configuration override"""
        with patch.dict(os.environ, {'OLLAMA_BASE_URL': 'http://env:11434'}):
            config_manager = EnhancedConfigManager(self.temp_dir)
            self.assertEqual(config_manager.ollama.base_url, "http://env:11434")


class TestOrchestrator(unittest.TestCase):
    """Test cases for the Orchestrator class."""

    def setUp(self):
        # Mock the GUI reference to prevent actual GUI interactions during tests
        self.mock_gui = Mock()
        self.orchestrator = Orchestrator(gui_ref=self.mock_gui)
        # Clear managed agents and tasks for a clean slate before each test
        self.orchestrator.managed_agents = []
        self.orchestrator.tasks = {}
        self.orchestrator.agent_name_counter = 0
        self.orchestrator.active_agents_count = 0
        self.orchestrator.active_search_agents_count = 0
        self.orchestrator.sub_agent_creation_allowed = False # Reset lock

    def test_agent_creation_and_dynamic_naming(self):
        """Test that agents are created with dynamic names and added to managed list."""
        initial_count = len(self.orchestrator.managed_agents)
        
        agent1 = self.orchestrator.create_agent("SearchAgent")
        self.assertIsNotNone(agent1)
        self.assertIn(agent1, self.orchestrator.managed_agents)
        self.assertTrue(agent1.name.startswith("Chimera") or agent1.name.startswith("Sentinel") or \
                        agent1.name.startswith("Nova") or agent1.name.startswith("Phoenix") or \
                        agent1.name.startswith("Vanguard") or agent1.name.startswith("Specter") or \
                        agent1.name.startswith("Oracle") or agent1.name.startswith("Echo"))
        self.assertEqual(len(self.orchestrator.managed_agents), initial_count + 1)
        self.assertEqual(self.orchestrator.active_agents_count, initial_count + 1)
        self.assertEqual(self.orchestrator.active_search_agents_count, 1)

        agent2 = self.orchestrator.create_agent("ProgrammingAgent")
        self.assertIsNotNone(agent2)
        self.assertIn(agent2, self.orchestrator.managed_agents)
        self.assertNotEqual(agent1.name, agent2.name) # Ensure dynamic naming is unique
        self.assertEqual(len(self.orchestrator.managed_agents), initial_count + 2)
        self.assertEqual(self.orchestrator.active_agents_count, initial_count + 2)
        self.assertEqual(self.orchestrator.active_search_agents_count, 1) # Should remain 1

        # Test unknown agent type
        unknown_agent = self.orchestrator.create_agent("UnknownAgent")
        self.assertIsNone(unknown_agent)
        self.orchestrator.error_logger.log_error.assert_called_with(
            "Orchestrator", "Unknown agent type: UnknownAgent"
        )

    def test_task_assignment_and_status_update(self):
        """Test task assignment to agents and subsequent status updates."""
        agent = self.orchestrator.create_agent("AgentBase")
        self.assertIsNotNone(agent)

        task_description = "Perform data analysis"
        task_id = self.orchestrator.assign_task(agent.name, task_description)
        
        self.assertIn(task_id, self.orchestrator.tasks)
        self.assertEqual(self.orchestrator.tasks[task_id]["description"], task_description)
        self.assertEqual(self.orchestrator.tasks[task_id]["assigned_to"], agent.name)
        self.assertEqual(self.orchestrator.tasks[task_id]["status"], "pending")
        self.assertEqual(self.orchestrator.tasks[task_id]["progress"], 0)

        # Simulate agent requesting task
        with patch.object(self.orchestrator.nexus_light, 'send_message') as mock_send_message:
            retrieved_task = self.orchestrator.get_available_task(agent.name)
            self.assertIsNotNone(retrieved_task)
            self.assertEqual(retrieved_task["task_id"], task_id)
            self.assertEqual(self.orchestrator.tasks[task_id]["status"], "in_progress")
            # get_available_task doesn't use nexus_light.send_message, it returns directly.
            # The agent's run loop would then use nexus_light.send_message to report completion.

        # Simulate agent updating task status
        self.orchestrator.update_task_status(task_id, "completed", agent.name, progress=100)
        self.assertEqual(self.orchestrator.tasks[task_id]["status"], "completed")
        self.assertEqual(self.orchestrator.tasks[task_id]["progress"], 100)

        # Test unauthorized task update
        another_agent = self.orchestrator.create_agent("AgentBase")
        self.orchestrator.update_task_status(task_id, "failed", another_agent.name)
        self.assertEqual(self.orchestrator.tasks[task_id]["status"], "completed") # Should not change
        self.orchestrator.error_logger.log_error.assert_called_with(
            "Orchestrator", f"Unauthorized task update attempt by {another_agent.name} for task {task_id}"
        )

        # Test update for non-existent task
        self.orchestrator.update_task_status("non-existent-task", "completed", agent.name)
        self.orchestrator.error_logger.log_error.assert_called_with(
            "Orchestrator", "Task ID not found: non-existent-task"
        )

    def test_kill_switch_functionality(self):
        """Test the Orchestrator's ability to activate an agent's kill switch."""
        agent = self.orchestrator.create_agent("AgentBase")
        self.assertIsNotNone(agent)
        self.assertTrue(agent.is_active)
        self.assertEqual(agent.status, "initialized")

        self.orchestrator.access_kill_switch(agent.name)
        self.assertFalse(agent.is_active)
        self.assertEqual(agent.status, "paused") # Orchestrator calls stop(), which sets to paused

        # Test kill switch for non-existent agent
        self.orchestrator.access_kill_switch("NonExistentAgent")
        self.orchestrator.error_logger.log_error.assert_called_with(
            "Orchestrator", "Agent NonExistentAgent not found for kill switch access."
        )

    def test_adjust_agent_count(self):
        """Test adjusting the total number of agents and maintaining Search Agent ratio."""
        # Initial state: 0 agents
        self.assertEqual(self.orchestrator.active_agents_count, 0)
        self.assertEqual(self.orchestrator.active_search_agents_count, 0)

        # Adjust to 3 agents (should create 1 SearchAgent, 2 AgentBase)
        self.orchestrator.adjust_agent_count(3)
        self.assertEqual(self.orchestrator.active_agents_count, 3)
        self.assertEqual(self.orchestrator.active_search_agents_count, 1)
        self.assertEqual(sum(1 for a in self.orchestrator.managed_agents if isinstance(a, AgentBase)), 2)
        self.assertEqual(sum(1 for a in self.orchestrator.managed_agents if isinstance(a, SearchAgent)), 1)

        # Adjust to 7 agents (should create 1 more SearchAgent, 3 more AgentBase)
        self.orchestrator.adjust_agent_count(7)
        self.assertEqual(self.orchestrator.active_agents_count, 7)
        self.assertEqual(self.orchestrator.active_search_agents_count, 2) # 7 // 5 = 1, but if total > 0, at least 1. 7 % 5 != 0, so 2.
        self.assertEqual(sum(1 for a in self.orchestrator.managed_agents if isinstance(a, AgentBase)), 5)
        self.assertEqual(sum(1 for a in self.orchestrator.managed_agents if isinstance(a, SearchAgent)), 2)

        # Adjust to 2 agents (should remove 5 agents, including SearchAgents if necessary)
        self.orchestrator.adjust_agent_count(2)
        self.assertEqual(self.orchestrator.active_agents_count, 2)
        self.assertEqual(self.orchestrator.active_search_agents_count, 1) # 2 // 5 = 0, but if total > 0, at least 1.

        # Adjust to 0 agents
        self.orchestrator.adjust_agent_count(0)
        self.assertEqual(self.orchestrator.active_agents_count, 0)
        self.assertEqual(self.orchestrator.active_search_agents_count, 0)
        self.assertEqual(len(self.orchestrator.managed_agents), 0)

        # Test negative desired count
        self.orchestrator.adjust_agent_count(-1)
        self.orchestrator.error_logger.log_error.assert_called_with(
            "Orchestrator", "Desired total agents cannot be negative."
        )

    def test_sub_agent_creation_lock(self):
        """Test the sub-agent creation lock mechanism."""
        # Initially, sub-agent creation is locked (False)
        self.assertFalse(self.orchestrator.sub_agent_creation_allowed)

        # Attempt to create a sub-agent (HoundAgent) by a non-main agent (AgentBase) - should fail
        mock_agent_base = Mock(spec=AgentBase, name="MockAgentBase")
        mock_agent_base.name = "MockAgentBase"
        mock_agent_base.is_sub_agent = False # Ensure it's not a sub-agent itself
        self.orchestrator.managed_agents.append(mock_agent_base) # Add to managed agents for creator_agent_type check

        hound_agent_fail = self.orchestrator.create_agent("HoundAgent", parent_agent_name="MockAgentBase", creator_agent_type="AgentBase")
        self.assertIsNone(hound_agent_fail)
        self.orchestrator.error_logger.log_error.assert_called_with(
            "Orchestrator", "Sub-agent creation by AgentBase is locked. Only main agents or Sentinel can create sub-agents when locked."
        )

        # Attempt to create a sub-agent (HoundAgent) by a main agent (SentinelAgent) - should succeed
        sentinel_agent = self.orchestrator.create_agent("SentinelAgent")
        self.assertIsNotNone(sentinel_agent)
        hound_agent_success = self.orchestrator.create_agent("HoundAgent", parent_agent_name=sentinel_agent.name, creator_agent_type="SentinelAgent")
        self.assertIsNotNone(hound_agent_success)
        self.assertIn(hound_agent_success, self.orchestrator.managed_agents)
        self.assertTrue(hound_agent_success.is_sub_agent)
        self.assertEqual(hound_agent_success.parent_agent_name, sentinel_agent.name)
        self.assertIn(hound_agent_success.name, self.orchestrator.agent_hierarchy[sentinel_agent.name])
        self.assertIn(hound_agent_success.name, sentinel_agent.sub_agents)

        # Toggle lock ON (allowed)
        self.orchestrator.set_sub_agent_creation_lock(True)
        self.assertTrue(self.orchestrator.sub_agent_creation_allowed)

        # Attempt to create a sub-agent (PupAgent) by a non-main agent (AgentBase) when lock is OFF - should succeed
        pup_agent_success = self.orchestrator.create_agent("PupAgent", parent_agent_name="MockAgentBase", creator_agent_type="AgentBase")
        self.assertIsNotNone(pup_agent_success)
        self.assertIn(pup_agent_success, self.orchestrator.managed_agents)
        self.assertTrue(pup_agent_success.is_sub_agent)
        self.assertEqual(pup_agent_success.parent_agent_name, "MockAgentBase")
        self.assertIn(pup_agent_success.name, self.orchestrator.agent_hierarchy["MockAgentBase"])
        self.assertIn(pup_agent_success.name, mock_agent_base.sub_agents) # Verify parent's sub_agents list updated

        # Toggle lock OFF (restricted) again
        self.orchestrator.set_sub_agent_creation_lock(False)
        self.assertFalse(self.orchestrator.sub_agent_creation_allowed)

    def test_handle_user_command_create_agent(self):
        """Test Orchestrator's handling of 'create agent' user command."""
        self.orchestrator.handle_user_command("create agent SearchAgent")
        self.mock_gui.append_terminal_output.assert_any_call(
            "Orchestrator: Successfully created ", output_type="System",
            # Using startswith because agent name is dynamic
            call_args_list=unittest.mock.call("Orchestrator: Successfully created ", output_type="System")
        )
        self.assertEqual(self.orchestrator.active_agents_count, 1)
        self.assertEqual(self.orchestrator.active_search_agents_count, 1)

        self.orchestrator.handle_user_command("create agent InvalidAgentType")
        self.mock_gui.append_terminal_output.assert_any_call(
            "Orchestrator: Failed to create InvalidAgentType. Check logs for details.", output_type="Error"
        )
        self.orchestrator.error_logger.log_error.assert_called_with(
            "Orchestrator", "Unknown agent type: InvalidAgentType"
        )

    def test_handle_user_command_list_agents(self):
        """Test Orchestrator's handling of 'list agents' user command."""
        self.orchestrator.handle_user_command("list agents")
        self.mock_gui.append_terminal_output.assert_any_call(
            "Orchestrator: No agents are currently managed.", output_type="System"
        )

        self.orchestrator.create_agent("ProgrammingAgent")
        self.orchestrator.create_agent("DebuggingOptimizationAgent")
        self.orchestrator.handle_user_command("list agents")
        # Check if the output contains the names of the created agents
        output_calls = [call for call in self.mock_gui.append_terminal_output.call_args_list if call.kwargs.get('output_type') == 'System']
        self.assertTrue(any("Orchestrator: Currently managed agents: " in call.args[0] for call in output_calls))
        self.assertEqual(self.orchestrator.active_agents_count, 2)

    def test_handle_user_command_kill_agent(self):
        """Test Orchestrator's handling of 'kill agent' user command."""
        agent = self.orchestrator.create_agent("AgentBase")
        self.assertIsNotNone(agent)
        agent_name = agent.name

        self.orchestrator.handle_user_command(f"kill agent {agent_name}")
        self.mock_gui.append_terminal_output.assert_any_call(
            f"Orchestrator: Kill switch activated for {agent_name}. Agent paused.", output_type="System"
        )
        self.assertFalse(agent.is_active)
        self.assertEqual(agent.status, "paused")

        self.orchestrator.handle_user_command("kill agent NonExistentAgent")
        self.mock_gui.append_terminal_output.assert_any_call(
            "Orchestrator: Agent NonExistentAgent not found for kill switch access.", output_type="Error"
        )

    def test_handle_user_command_adjust_agents(self):
        """Test Orchestrator's handling of 'adjust agents' user command."""
        self.orchestrator.handle_user_command("adjust agents 2")
        self.mock_gui.append_terminal_output.assert_any_call(
            "Orchestrator: Adjusting total agents to 2...", output_type="System"
        )
        self.assertEqual(self.orchestrator.active_agents_count, 2)
        self.assertTrue(self.orchestrator.active_search_agents_count >= 0) # Can be 0 or 1 depending on random choice

        self.orchestrator.handle_user_command("adjust agents invalid")
        self.mock_gui.append_terminal_output.assert_any_call(
            "Orchestrator: Invalid number for 'adjust agents'. Usage: 'adjust agents [number]'.", output_type="Error"
        )

    def test_handle_user_command_toggle_sub_agent_lock(self):
        """Test Orchestrator's handling of 'toggle sub-agent lock' user command."""
        initial_lock_status = self.orchestrator.sub_agent_creation_allowed
        self.orchestrator.handle_user_command("toggle sub-agent lock")
        self.assertNotEqual(self.orchestrator.sub_agent_creation_allowed, initial_lock_status)
        self.mock_gui.append_terminal_output.assert_any_call(
            f"Sub-Agent Creation Lock toggled to {'OFF (Allowed)' if self.orchestrator.sub_agent_creation_allowed else 'ON (Restricted)'}.", output_type="System"
        )

    def test_handle_error_and_trigger_auto_fix(self):
        """Test error handling and simulated auto-fix triggering."""
        agent_name = "TestAgent"
        error_message = "Simulated error occurred."
        details = {"code": 123, "severity": "high"}

        self.orchestrator.handle_error(agent_name, error_message, details)
        self.orchestrator.error_logger.log_error.assert_called_with(
            agent_name, error_message, details
        )
        self.mock_gui.display_error.assert_called_with({
            "agent_name": agent_name,
            "message": error_message,
            "details": details,
            "auto_fix_available": True,
            "fixed": False
        })

        # Simulate GUI triggering auto-fix
        self.orchestrator.trigger_auto_fix(agent_name, error_message, details)
        self.mock_gui.update_chat_display.assert_called_with(
            "Orchestrator", f"Orchestrator attempting to auto-fix error from {agent_name}..."
        )
        self.mock_gui.display_error.assert_called_with({
            "agent_name": agent_name,
            "message": error_message,
            "details": details,
            "auto_fix_available": False,
            "fixed": True
        })

    def test_handle_message_request_task(self):
        """Test Orchestrator's handling of 'request_task' messages from agents."""
        agent = self.orchestrator.create_agent("AgentBase")
        self.assertIsNotNone(agent)
        task_description = "New task for agent"
        self.orchestrator.assign_task(None, task_description, initial_status="pending") # Assign unassigned task

        with patch.object(self.orchestrator.nexus_light, 'send_message') as mock_send_message:
            self.orchestrator.handle_message(agent.name, {"type": "request_task"})
            mock_send_message.assert_called_once()
            args, kwargs = mock_send_message.call_args
            self.assertEqual(kwargs['recipient_agent_name'], agent.name)
            self.assertEqual(kwargs['message_payload']['type'], "task_assigned")
            self.assertIn("task", kwargs['message_payload'])
            self.assertEqual(kwargs['message_payload']['task']['description'], task_description)
            
            # Verify task status updated to in_progress
            task_id = kwargs['message_payload']['task']['task_id']
            self.assertEqual(self.orchestrator.tasks[task_id]["status"], "in_progress")

        # Test no task available
        self.orchestrator.tasks = {} # Clear tasks
        with patch.object(self.orchestrator.nexus_light, 'send_message') as mock_send_message:
            self.orchestrator.handle_message(agent.name, {"type": "request_task"})
            mock_send_message.assert_called_once()
            args, kwargs = mock_send_message.call_args
            self.assertEqual(kwargs['recipient_agent_name'], agent.name)
            self.assertEqual(kwargs['message_payload']['type'], "no_task_available")

    def test_handle_message_task_completed(self):
        """Test Orchestrator's handling of 'task_completed' messages from agents."""
        agent = self.orchestrator.create_agent("AgentBase")
        self.assertIsNotNone(agent)
        task_description = "Task to be completed"
        task_id = self.orchestrator.assign_task(agent.name, task_description, initial_status="in_progress")

        self.orchestrator.handle_message(agent.name, {
            "type": "task_completed",
            "task_id": task_id,
            "status": "completed",
            "content": "Task done."
        })
        self.assertEqual(self.orchestrator.tasks[task_id]["status"], "completed")
        self.mock_gui.update_task_board.assert_called()

        # Test missing task_id
        self.orchestrator.handle_message(agent.name, {
            "type": "task_completed",
            "status": "completed",
            "content": "Task done."
        })
        self.orchestrator.error_logger.log_error.assert_called_with(
            "Orchestrator", f"'task_completed' message missing task_id from {agent.name}"
        )

    def test_get_all_tasks_for_gui(self):
        """Test retrieval of all tasks for GUI display, including simulated progress."""
        agent1 = self.orchestrator.create_agent("AgentBase")
        agent2 = self.orchestrator.create_agent("AgentBase")

        task1_id = self.orchestrator.assign_task(agent1.name, "Task One", initial_status="pending")
        task2_id = self.orchestrator.assign_task(agent2.name, "Task Two", initial_status="in_progress", initial_progress=50)
        task3_id = self.orchestrator.assign_task(agent1.name, "Task Three", initial_status="completed", initial_progress=100)

        tasks_for_gui = self.orchestrator.get_all_tasks_for_gui()
        self.assertEqual(len(tasks_for_gui), 3)

        task1_info = next(t for t in tasks_for_gui if t["task_id"] == task1_id)
        task2_info = next(t for t in tasks_for_gui if t["task_id"] == task2_id)
        task3_info = next(t for t in tasks_for_gui if t["task_id"] == task3_id)

        # Task 1 should have potentially moved to in_progress
        self.assertIn(task1_info["status"], ["pending", "in_progress"])
        # Task 2 should have incremented progress (or completed)
        self.assertTrue(task2_info["progress"] >= 50)
        # Task 3 should remain completed
        self.assertEqual(task3_info["status"], "completed")
        self.assertEqual(task3_info["progress"], 100)

    def test_get_overall_project_progress(self):
        """Test calculation of overall project progress."""
        self.assertEqual(self.orchestrator.get_overall_project_progress(), 0)

        agent = self.orchestrator.create_agent("AgentBase")
        self.orchestrator.assign_task(agent.name, "Task A", initial_progress=20)
        self.orchestrator.assign_task(agent.name, "Task B", initial_progress=60)
        self.orchestrator.assign_task(agent.name, "Task C", initial_progress=100)

        # (20 + 60 + 100) / 3 = 180 / 3 = 60
        self.assertEqual(self.orchestrator.get_overall_project_progress(), 60)

        # Test with no tasks
        self.orchestrator.tasks = {}
        self.assertEqual(self.orchestrator.get_overall_project_progress(), 0)

    def test_customize_os_file_security_check(self):
        """Test the security check for customizing OS files."""
        # Test allowed file
        allowed_file = "/etc/my_config.conf"
        self.assertTrue(self.orchestrator.customize_os_file(allowed_file, "new content"))
        self.orchestrator.error_logger.log_error.assert_not_called()

        # Test blocked networking file
        blocked_file = "/etc/network/interfaces"
        self.assertFalse(self.orchestrator.customize_os_file(blocked_file, "new content"))
        self.orchestrator.error_logger.log_error.assert_called_with(
            "Orchestrator",
            f"Blocked attempt to edit networking file: {blocked_file}",
            {"file_path": blocked_file, "reason": "Networking files are Search Agent exclusive"}
        )
        self.orchestrator.error_logger.log_error.reset_mock() # Reset mock for next test

        blocked_file_2 = "/etc/resolv.conf"
        self.assertFalse(self.orchestrator.customize_os_file(blocked_file_2, "new content"))
        self.orchestrator.error_logger.log_error.assert_called_with(
            "Orchestrator",
            f"Blocked attempt to edit networking file: {blocked_file_2}",
            {"file_path": blocked_file_2, "reason": "Networking files are Search Agent exclusive"}
        )

    def test_get_agent_team_count(self):
        """Test retrieving the count of direct sub-agents."""
        parent_agent = self.orchestrator.create_agent("SentinelAgent")
        self.assertIsNotNone(parent_agent)
        self.assertEqual(self.orchestrator.get_agent_team_count(parent_agent.name), 0)

        # Create sub-agents
        self.orchestrator.set_sub_agent_creation_lock(True) # Allow sub-agent creation for this test
        self.orchestrator.create_agent("HoundAgent", parent_agent_name=parent_agent.name, creator_agent_type="SentinelAgent")
        self.orchestrator.create_agent("PupAgent", parent_agent_name=parent_agent.name, creator_agent_type="SentinelAgent")

        self.assertEqual(self.orchestrator.get_agent_team_count(parent_agent.name), 2)

        # Test for an agent with no sub-agents
        other_agent = self.orchestrator.create_agent("ProgrammingAgent")
        self.assertEqual(self.orchestrator.get_agent_team_count(other_agent.name), 0)

        # Test for non-existent agent
        self.assertEqual(self.orchestrator.get_agent_team_count("NonExistentAgent"), 0)

    def test_receive_user_directive(self):
        """Test the Orchestrator's ability to receive and break down user directives."""
        # Ensure there's at least one agent for task assignment
        self.orchestrator.create_agent("SearchAgent")

        directive = "Develop a new feature for the AI Agent Army GUI."
        self.orchestrator.receive_user_directive(directive)

        # Verify that tasks were created
        self.assertGreaterEqual(len(self.orchestrator.tasks), 2) # Expecting 2-3 tasks
        
        # Verify task descriptions contain parts of the directive
        task_descriptions = [task["description"] for task in self.orchestrator.tasks.values()]
        self.assertTrue(any("Analyze" in desc for desc in task_descriptions))
        self.assertTrue(any("Develop initial plan" in desc for desc in task_descriptions))
        self.assertTrue(any("Assign agents" in desc for desc in task_descriptions))

        # Verify GUI updates were called
        self.mock_gui.append_terminal_output.assert_any_call(
            f"Orchestrator: Breaking down directive: '{directive}' into sub-tasks...", output_type="Narrative"
        )
        self.mock_gui.update_task_board.assert_called()
        self.mock_gui.append_terminal_output.assert_any_call(
            unittest.mock.ANY, output_type="System" # Check for the "Simulated breakdown complete" message
        )

    def test_get_system_metrics(self):
        """Test the simulated system metrics retrieval."""
        metrics = self.orchestrator.get_system_metrics()
        self.assertIn("cpu", metrics)
        self.assertIn("memory", metrics)
        self.assertIn("nvme", metrics)
        self.assertIn("gpu", metrics)
        self.assertIn("network_in", metrics)
        self.assertIn("network_out", metrics)
        self.assertIn("uptime", metrics)

        # Check format (e.g., "XX.XX%")
        self.assertRegex(metrics["cpu"], r"^\d{1,2}\.\d{2}%$")
        self.assertRegex(metrics["memory"], r"^\d+\.\d{2}GB / \d+GB$")
        self.assertRegex(metrics["uptime"], r"^\d{2}:\d{2}:\d{2}$")

    def test_get_git_status(self):
        """Test the simulated Git status retrieval."""
        status = self.orchestrator.get_git_status()
        self.assertIsInstance(status, str)
        self.assertTrue(status.startswith("Git Status:"))

    def test_get_sandbox_performance_data(self):
        """Test the simulated sandbox performance data retrieval."""
        data = self.orchestrator.get_sandbox_performance_data()
        self.assertIn("latency", data)
        self.assertIn("cpu", data)
        self.assertIn("ram", data)
        self.assertIn("status", data)

        self.assertRegex(data["latency"], r"^\d+ms$")
        self.assertRegex(data["cpu"], r"^\d+%$")
        self.assertRegex(data["ram"], r"^\d+MB$")
        self.assertIn(data["status"], ["Success", "Failure", "Running Tests..."])

    def test_handle_new_device_detection(self):
        """Test handling of new device detection."""
        device_info_personal = {"name": "USB Drive", "type": "storage", "is_personal": True}
        device_info_non_personal = {"name": "Network Adapter", "type": "network", "is_personal": False}

        # Test personal device (requires permission)
        self.orchestrator.handle_new_device_detection(device_info_personal)
        self.mock_gui.update_chat_display.assert_called_with(
            "Orchestrator", f"Permission required for personal device: {device_info_personal['name']}. Optimize?"
        )
        self.mock_gui.update_chat_display.reset_mock() # Reset mock for next test

        # Test non-personal device (proceeds directly)
        self.orchestrator.handle_new_device_detection(device_info_non_personal)
        self.mock_gui.update_chat_display.assert_not_called() # Should not ask for permission

    def test_settings_management(self):
        """Test simulated settings management methods."""
        # API Keys
        initial_keys = self.orchestrator.get_api_keys()
        self.orchestrator.add_api_key("new_key_789")
        self.assertIn("new_key_789", self.orchestrator.api_keys)
        self.orchestrator.edit_api_key("new_key_789", "edited_key_789")
        self.assertIn("edited_key_789", self.orchestrator.api_keys)
        self.assertNotIn("new_key_789", self.orchestrator.api_keys)
        self.orchestrator.delete_api_key("edited_key_789")
        self.assertNotIn("edited_key_789", self.orchestrator.api_keys)
        self.assertEqual(self.orchestrator.api_keys, initial_keys) # Should be back to original

        # LLM Selection
        self.orchestrator.set_llm_selection("GPT-4", "Inherit")
        self.assertEqual(self.orchestrator.llm_selection["primary"], "GPT-4")
        self.assertEqual(self.orchestrator.llm_selection["sub_agent_default"], "Inherit")

        # Theme Settings
        self.orchestrator.set_theme_settings("#FF0000", 90)
        self.assertEqual(self.orchestrator.theme_settings["accent_color"], "#FF0000")
        self.assertEqual(self.orchestrator.theme_settings["dark_mode_intensity"], 90)

        # Persistence Settings
        self.orchestrator.set_persistence_settings(False, True)
        self.assertFalse(self.orchestrator.persistence_settings["auto_save"])
        self.assertTrue(self.orchestrator.persistence_settings["backups"])

        # Log Configuration
        self.orchestrator.set_log_configuration("Debug", 7)
        self.assertEqual(self.orchestrator.log_config["verbosity"], "Debug")
        self.assertEqual(self.orchestrator.log_config["retention_days"], 7)

        # Notification Preferences
        self.orchestrator.set_notification_preferences(False, True)
        self.assertFalse(self.orchestrator.notification_prefs["on_errors"])
        self.assertTrue(self.orchestrator.notification_prefs["on_status_changes"])


class TestAgentBase(unittest.TestCase):
    """Test cases for the AgentBase class."""

    def setUp(self):
        self.mock_orchestrator = Mock()
        self.mock_orchestrator.gui_ref = Mock() # Mock GUI reference within orchestrator
        self.agent = AgentBase(name="TestAgent", orchestrator=self.mock_orchestrator)

    def test_initialization(self):
        """Test AgentBase initialization."""
        self.assertEqual(self.agent.name, "TestAgent")
        self.assertEqual(self.agent.status, "initialized")
        self.assertTrue(self.agent.is_active)
        self.assertFalse(self.agent._is_killed)
        self.assertIsNotNone(self.agent.rag_database)
        self.assertIsNone(self.agent.context_7_mcp_server) # Main agents use RAGDatabase
        self.assertIsNotNone(self.agent.error_logger)
        self.assertIsNotNone(self.agent.nexus_light)
        self.assertEqual(self.agent.error_logger.orchestrator_ref, self.mock_orchestrator)
        self.assertEqual(self.agent.nexus_light.orchestrator_ref, self.mock_orchestrator)

        # Test sub-agent initialization
        sub_agent = AgentBase(name="SubAgent", orchestrator=self.mock_orchestrator, is_sub_agent=True, parent_agent_name="ParentAgent")
        self.assertTrue(sub_agent.is_sub_agent)
        self.assertEqual(sub_agent.parent_agent_name, "ParentAgent")
        self.assertIsNone(sub_agent.rag_database)
        self.assertIsNotNone(sub_agent.context_7_mcp_server) # Sub-agents use Context7MCPServer

    def test_set_status(self):
        """Test setting agent status."""
        self.agent.set_status("working")
        self.assertEqual(self.agent.status, "working")
        self.agent.set_status("idle")
        self.assertEqual(self.agent.status, "idle")

    def test_stop_and_kill_switch(self):
        """Test stop and kill switch functionality."""
        self.assertTrue(self.agent.is_active)
        self.assertFalse(self.agent._is_killed)

        self.agent.stop()
        self.assertFalse(self.agent.is_active)
        self.assertEqual(self.agent.status, "paused")
        self.mock_orchestrator.gui_ref.update_kill_switch_status.assert_called_with(self.agent.name, False)

        self.agent.is_active = True # Reset for kill test
        self.agent.kill()
        self.assertFalse(self.agent.is_active)
        self.assertTrue(self.agent._is_killed)
        self.assertEqual(self.agent.status, "killed")

    def test_update_system_prompt(self):
        """Test updating the agent's system prompt."""
        initial_prompt = self.agent.system_prompt
        new_prompt = "You are a specialized debugging agent."
        self.agent.update_system_prompt(new_prompt)
        self.assertEqual(self.agent.system_prompt, new_prompt)
        self.assertNotEqual(self.agent.system_prompt, initial_prompt)

    @patch('time.sleep', return_value=None) # Mock time.sleep to speed up tests
    def test_run_loop_task_assignment_and_completion(self, mock_sleep):
        """Test the basic run loop behavior with task assignment and completion."""
        # Mock Orchestrator's get_available_task to return a task
        mock_task_id = str(uuid.uuid4())
        mock_task = {"task_id": mock_task_id, "description": "Simulated Task"}
        self.mock_orchestrator.get_available_task.side_effect = [
            mock_task, # First call returns a task
            None # Subsequent calls return no task
        ]
        
        # Mock NexusLight's send_message to capture task completion
        with patch.object(self.agent.nexus_light, 'send_message') as mock_send_message:
            # Mock NexusLight's query_orchestrator_for_directives
            with patch.object(self.agent.nexus_light, 'query_orchestrator_for_directives') as mock_query_directives:
                mock_query_directives.side_effect = [
                    {"type": "task_assigned", "task": mock_task},
                    {"type": "no_task_available", "content": "No tasks currently available."}
                ]

                # Run the agent for a short period to simulate one task cycle
                # We need to control the loop, so we'll set is_active to False after one iteration
                def stop_after_one_task(*args, **kwargs):
                    self.agent.is_active = False
                    return mock_task
                self.mock_orchestrator.get_available_task.side_effect = stop_after_one_task

                self.agent.run()

                # Verify status changes
                self.assertEqual(self.agent.status, "completed_task") # Should complete the task and then stop

                # Verify task completion message was sent
                mock_send_message.assert_called_once()
                args, kwargs = mock_send_message.call_args
                self.assertEqual(kwargs['recipient_agent_name'], "Orchestrator")
                self.assertEqual(kwargs['message_payload']['type'], "task_completed")
                self.assertEqual(kwargs['message_payload']['task_id'], mock_task_id)
                self.assertEqual(kwargs['message_payload']['status'], "completed")

                # Verify GUI updates
                self.mock_orchestrator.gui_ref.update_chat_display.assert_any_call(
                    self.agent.name, f"Received task: {mock_task['description']}"
                )
                self.mock_orchestrator.gui_ref.append_terminal_output.assert_any_call(
                    f"Narrative: {self.agent.name} received task: '{mock_task['description']}'.", output_type="Narrative"
                )
                self.mock_orchestrator.gui_ref.append_terminal_output.assert_any_call(
                    unittest.mock.ANY, output_type="Code Snippet"
                )
                self.mock_orchestrator.gui_ref.append_terminal_output.assert_any_call(
                    unittest.mock.ANY, output_type="Narrative"
                )
                self.mock_orchestrator.gui_ref.append_terminal_output.assert_any_call(
                    f"Agent {self.agent.name} gracefully stopped/paused.", output_type="System"
                )

    @patch('time.sleep', return_value=None)
    def test_run_loop_no_task_available(self, mock_sleep):
        """Test run loop behavior when no tasks are available."""
        self.mock_orchestrator.get_available_task.return_value = None # Always return no task

        # Mock NexusLight's query_orchestrator_for_directives
        with patch.object(self.agent.nexus_light, 'query_orchestrator_for_directives') as mock_query_directives:
            mock_query_directives.return_value = {"type": "no_task_available", "content": "No tasks currently available."}

            # Run the agent for a very short period, then stop it
            def stop_after_idle_check(*args, **kwargs):
                self.agent.is_active = False
                return None
            self.mock_orchestrator.get_available_task.side_effect = stop_after_idle_check

            self.agent.run()

            self.assertEqual(self.agent.status, "stopped")
            self.mock_orchestrator.gui_ref.append_terminal_output.assert_any_call(
                f"Narrative: {self.agent.name} found no tasks available and will wait.", output_type="Narrative"
            )

    @patch('time.sleep', return_value=None)
    def test_run_loop_error_handling(self, mock_sleep):
        """Test run loop behavior when an unhandled exception occurs."""
        # Simulate an error during task retrieval
        self.mock_orchestrator.get_available_task.side_effect = Exception("Simulated network error")

        # Mock NexusLight's query_orchestrator_for_directives
        with patch.object(self.agent.nexus_light, 'query_orchestrator_for_directives') as mock_query_directives:
            mock_query_directives.side_effect = Exception("Simulated network error")

            # Run the agent for a short period, then stop it
            def stop_after_error(*args, **kwargs):
                self.agent.is_active = False
                raise Exception("Simulated network error") # Raise error to trigger handler
            self.mock_orchestrator.get_available_task.side_effect = stop_after_error

            self.agent.run()

            self.assertEqual(self.agent.status, "error")
            self.agent.error_logger.log_error.assert_called_once()
            self.mock_orchestrator.gui_ref.append_terminal_output.assert_any_call(
                f"Agent {self.agent.name} encountered an error: Unhandled exception in agent {self.agent.name}: Simulated network error", output_type="Error"
            )

    def test_report_status_to_gui(self):
        """Test reporting agent status to GUI."""
        self.agent.report_status_to_gui()
        self.mock_orchestrator.gui_ref.update_agent_status.assert_called_once()
        args, kwargs = self.mock_orchestrator.gui_ref.update_agent_status.call_args
        self.assertEqual(args[0], self.agent.name)
        self.assertIn("running_time", kwargs)
        self.assertIn("cpu_usage", kwargs)
        self.assertIn("memory_usage", kwargs)
        self.assertIn("network_usage", kwargs)
        self.assertRegex(kwargs["running_time"], r"^\d{2}h \d{2}m \d{2}s$")
        self.assertRegex(kwargs["cpu_usage"], r"^\d+\.\d{2}%$")
        self.assertRegex(kwargs["memory_usage"], r"^\d+\.\d{2}MB$")
        self.assertRegex(kwargs["network_usage"], r"^In \d+\.\d{2}KB/s / Out \d+\.\d{2}KB/s$")

    def test_add_sub_agent(self):
        """Test adding a sub-agent to the agent's list."""
        self.assertEqual(len(self.agent.sub_agents), 0)
        self.agent.add_sub_agent("ChildAgent1")
        self.assertEqual(self.agent.sub_agents, ["ChildAgent1"])
        self.agent.add_sub_agent("ChildAgent2")
        self.assertEqual(self.agent.sub_agents, ["ChildAgent1", "ChildAgent2"])
        # Ensure no duplicates are added
        self.agent.add_sub_agent("ChildAgent1")
        self.assertEqual(self.agent.sub_agents, ["ChildAgent1", "ChildAgent2"])


class TestErrorLogger(unittest.TestCase):
    """Test cases for the ErrorLogger class."""

    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.error_log_dir = os.path.join(self.temp_dir, "error")
        self.mock_orchestrator = Mock()
        self.error_logger = ErrorLogger(base_dir=self.temp_dir, orchestrator_ref=self.mock_orchestrator)

    def tearDown(self):
        shutil.rmtree(self.temp_dir)

    def test_initialization(self):
        """Test that the error log directory is created."""
        self.assertTrue(os.path.exists(self.error_log_dir))
        self.assertEqual(self.error_logger.error_log_dir, self.error_log_dir)
        self.assertEqual(self.error_logger.orchestrator_ref, self.mock_orchestrator)

    def test_log_error_to_file(self):
        """Test that errors are logged to a JSON file with correct format."""
        agent_name = "TestAgent"
        error_message = "Simulated file write error."
        details = {"file_path": "/non/existent/path", "reason": "Permission denied"}

        self.error_logger.log_error(agent_name, error_message, details)

        # Verify that a file was created in the error log directory
        logged_files = os.listdir(self.error_log_dir)
        self.assertEqual(len(logged_files), 1)
        log_filepath = os.path.join(self.error_log_dir, logged_files[0])
        self.assertTrue(log_filepath.endswith(".json"))

        # Read the logged file and verify its content
        with open(log_filepath, 'r') as f:
            logged_data = json.load(f)

        self.assertIn("timestamp", logged_data)
        self.assertIn("agent_name", logged_data)
        self.assertIn("error_message", logged_data)
        self.assertIn("details", logged_data)

        self.assertEqual(logged_data["agent_name"], agent_name)
        self.assertEqual(logged_data["error_message"], error_message)
        self.assertEqual(logged_data["details"], details)

        # Verify timestamp format (ISO 8601)
        try:
            datetime.datetime.fromisoformat(logged_data["timestamp"])
        except ValueError:
            self.fail("Logged timestamp is not in ISO 8601 format.")

        # Verify orchestrator's handle_error was called
        self.mock_orchestrator.handle_error.assert_called_once_with(
            agent_name, error_message, details
        )

    def test_log_error_without_details(self):
        """Test logging an error without additional details."""
        agent_name = "AnotherAgent"
        error_message = "Generic error."

        self.error_logger.log_error(agent_name, error_message)

        logged_files = os.listdir(self.error_log_dir)
        self.assertEqual(len(logged_files), 1)
        log_filepath = os.path.join(self.error_log_dir, logged_files[0])

        with open(log_filepath, 'r') as f:
            logged_data = json.load(f)

        self.assertIsNone(logged_data["details"])
        self.assertEqual(logged_data["agent_name"], agent_name)
        self.assertEqual(logged_data["error_message"], error_message)
        self.mock_orchestrator.handle_error.assert_called_once_with(
            agent_name, error_message, None
        )

    @patch('os.makedirs', side_effect=IOError("Disk full"))
    def test_initialization_io_error(self, mock_makedirs):
        """Test error handling during directory creation."""
        # This test ensures that if os.makedirs fails, it's handled gracefully
        # The ErrorLogger constructor itself prints an error, but doesn't raise.
        # We primarily check that it doesn't crash.
        with self.assertRaises(IOError): # The mock raises it, not the ErrorLogger
            ErrorLogger(base_dir="/non/writable/path")
        # The actual ErrorLogger would print an error, but the mock prevents it from being created fully.
        # So, we check the mock was called and raised the error.


class TestNexusLight(unittest.TestCase):
    """Test cases for the NexusLight class."""

    def setUp(self):
        self.mock_orchestrator = Mock()
        self.nexus_light = NexusLight(agent_name="TestAgent", orchestrator_ref=self.mock_orchestrator)

    def test_initialization(self):
        """Test NexusLight initialization."""
        self.assertEqual(self.nexus_light.agent_name, "TestAgent")
        self.assertEqual(self.nexus_light.orchestrator_ref, self.mock_orchestrator)
        self.assertIsNone(self.nexus_light.parent_agent_name)

        # Test with parent agent name
        sub_nexus_light = NexusLight(agent_name="SubAgent", orchestrator_ref=self.mock_orchestrator, parent_agent_name="ParentAgent")
        self.assertEqual(sub_nexus_light.parent_agent_name, "ParentAgent")

    def test_send_message(self):
        """Test sending a message via NexusLight."""
        recipient = "RecipientAgent"
        message_payload = {"type": "chat", "content": "Hello there!"}
        
        self.nexus_light.send_message(recipient, message_payload)
        
        # Verify that the orchestrator's handle_message was called with correct arguments
        self.mock_orchestrator.handle_message.assert_called_once()
        args, kwargs = self.mock_orchestrator.handle_message.call_args
        
        self.assertEqual(args[0], "TestAgent") # sender_agent_name
        
        received_payload = args[1] # message_payload
        self.assertEqual(received_payload["type"], "chat")
        self.assertEqual(received_payload["content"], "Hello there!")
        self.assertEqual(received_payload["sender_name"], "TestAgent")
        self.assertIsNone(received_payload["sender_parent"])
        self.assertEqual(received_payload["recipient_name"], recipient)
        self.assertEqual(received_payload["sender_hierarchy"], ["TestAgent"])
        self.assertEqual(received_payload["recipient_hierarchy"], [recipient])

    def test_receive_message(self):
        """Test receiving a message via NexusLight."""
        sender = "SenderAgent"
        message_payload = {"type": "status", "content": "I am idle.", "sender_name": "SenderAgent", "recipient_name": "TestAgent"}
        
        self.nexus_light.receive_message(sender, message_payload)
        
        # Verify that the orchestrator's handle_message was called with correct arguments
        self.mock_orchestrator.handle_message.assert_called_once_with(sender, message_payload)

    def test_query_orchestrator_for_directives_task_available(self):
        """Test querying orchestrator for directives when a task is available."""
        mock_task = {"task_id": "123", "description": "Do something"}
        self.mock_orchestrator.get_available_task.return_value = mock_task

        response = self.nexus_light.query_orchestrator_for_directives()
        
        self.mock_orchestrator.get_available_task.assert_called_once_with("TestAgent")
        self.assertEqual(response["type"], "task_assigned")
        self.assertEqual(response["task"], mock_task)

    def test_query_orchestrator_for_directives_no_task_available(self):
        """Test querying orchestrator for directives when no task is available."""
        self.mock_orchestrator.get_available_task.return_value = None

        response = self.nexus_light.query_orchestrator_for_directives()
        
        self.mock_orchestrator.get_available_task.assert_called_once_with("TestAgent")
        self.assertEqual(response["type"], "no_task_available")
        self.assertIn("No tasks currently available.", response["content"])

    def test_query_orchestrator_for_directives_no_orchestrator_ref(self):
        """Test querying orchestrator when orchestrator_ref is not set."""
        nexus_light_no_orchestrator = NexusLight(agent_name="AgentX")
        response = nexus_light_no_orchestrator.query_orchestrator_for_directives()
        
        self.assertEqual(response["type"], "error")
        self.assertIn("Orchestrator reference not set.", response["content"])


class TestEnhancedConnectionHandler(unittest.TestCase):
    """Test enhanced connection handling"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.api = EnhancedOllamaAPI(config_file=os.path.join(self.temp_dir, "test_config.json"))
    
    def tearDown(self):
        self.api.close()
        shutil.rmtree(self.temp_dir)
    
    @patch('requests.Session.get')
    def test_successful_model_listing(self, mock_get):
        """Test successful model listing"""
        # Mock successful response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "models": [
                {"name": "llama2"},
                {"name": "llama3.2:1b"}
            ]
        }
        mock_get.return_value = mock_response
        
        models = self.api.list_models()
        self.assertEqual(len(models), 2)
        self.assertIn("llama2", models)
        self.assertIn("llama3.2:1b", models)
    
    @patch('requests.Session.get')
    def test_connection_failure_handling(self, mock_get):
        """Test connection failure handling"""
        # Mock connection error
        mock_get.side_effect = ConnectionError("Connection refused")
        
        models = self.api.list_models()
        self.assertEqual(len(models), 0)
        self.assertEqual(self.api.connection_state, ConnectionState.FAILED)
    
    @patch('requests.Session.post')
    def test_chat_with_retry(self, mock_post):
        """Test chat functionality with retry logic"""
        # First call fails, second succeeds
        mock_response_fail = Mock()
        mock_response_fail.status_code = 500
        
        mock_response_success = Mock()
        mock_response_success.json.return_value = {
            "message": {"content": "Hello!"}
        }
        
        mock_post.side_effect = [mock_response_fail, mock_response_success]
        
        response = self.api.chat("llama2", [{"role": "user", "content": "Hi"}], stream=False)
        self.assertEqual(response.status_code, 200)
    
    def test_connection_status_monitoring(self):
        """Test connection status monitoring"""
        status = self.api.get_connection_status()
        self.assertIn("state", status)
        self.assertIn("consecutive_failures", status)
        self.assertIn("base_url", status)


class TestEnhancedErrorHandler(unittest.TestCase):
    """Test enhanced error handling"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.error_handler = EnhancedErrorHandler(
            log_file=os.path.join(self.temp_dir, "test_errors.log")
        )
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir)
    
    def test_error_categorization(self):
        """Test error categorization and severity"""
        test_error = ValueError("Test error")
        
        success = self.error_handler.handle_error(
            test_error,
            ErrorCategory.VALIDATION,
            ErrorSeverity.HIGH,
            {"test_context": "test_value"}
        )
        
        self.assertFalse(success)  # No recovery strategy for this test
        self.assertEqual(len(self.error_handler.errors), 1)
        
        error_info = self.error_handler.errors[0]
        self.assertEqual(error_info.category, ErrorCategory.VALIDATION)
        self.assertEqual(error_info.severity, ErrorSeverity.HIGH)
        self.assertEqual(error_info.message, "Test error")
    
    def test_error_statistics(self):
        """Test error statistics generation"""
        # Generate multiple errors
        for i in range(5):
            self.error_handler.handle_error(
                ValueError(f"Error {i}"),
                ErrorCategory.CONNECTION,
                ErrorSeverity.MEDIUM
            )
        
        stats = self.error_handler.get_error_statistics()
        self.assertEqual(stats["total_errors"], 5)
        self.assertEqual(stats["category_breakdown"]["connection"], 5)
        self.assertEqual(stats["severity_breakdown"]["medium"], 5)
    
    def test_recovery_strategy_registration(self):
        """Test custom recovery strategy registration"""
        def custom_recovery(error_info):
            return True
        
        self.error_handler.register_recovery_strategy(
            ErrorCategory.VALIDATION,
            custom_recovery
        )
        
        # Test recovery
        test_error = ValueError("Test error")
        success = self.error_handler.handle_error(
            test_error,
            ErrorCategory.VALIDATION,
            ErrorSeverity.MEDIUM
        )
        
        self.assertTrue(success)
    
    def test_error_export(self):
        """Test error report export"""
        # Generate test error
        self.error_handler.handle_error(
            RuntimeError("Test export error"),
            ErrorCategory.SYSTEM,
            ErrorSeverity.HIGH
        )
        
        export_path = os.path.join(self.temp_dir, "error_report.json")
        self.error_handler.export_error_report(export_path)
        
        self.assertTrue(os.path.exists(export_path))
        
        with open(export_path, 'r') as f:
            report = json.load(f)
        
        self.assertIn("statistics", report)
        self.assertIn("recent_errors", report)
        self.assertEqual(report["statistics"]["total_errors"], 1)


class TestAutoLoopSystem(unittest.TestCase):
    """Test auto-loop system functionality"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.auto_loop = AutoLoopSystem(
            config_file=os.path.join(self.temp_dir, "auto_loop_config.json")
        )
    
    def tearDown(self):
        self.auto_loop.stop_auto_loop()
        shutil.rmtree(self.temp_dir)
    
    def test_operation_addition(self):
        """Test adding operations to the auto-loop system"""
        def test_operation():
            return True
        
        op_id = self.auto_loop.add_operation(
            "test_op_1",
            "Test Operation",
            test_operation,
            max_retries=2
        )
        
        self.assertIn(op_id, self.auto_loop.operations)
        operation = self.auto_loop.operations[op_id]
        self.assertEqual(operation.name, "Test Operation")
        self.assertEqual(operation.max_retries, 2)
        self.assertEqual(operation.status, OperationStatus.PENDING)
    
    def test_operation_execution(self):
        """Test operation execution"""
        def failing_operation():
            raise ValueError("Test failure")
        
        # Test successful operation
        op_id_success = self.auto_loop.add_operation(
            "test_success",
            "Successful Operation",
            lambda: True # Use lambda for simple success
        )
        
        success = self.auto_loop.execute_operation(op_id_success)
        self.assertTrue(success)
        self.assertEqual(self.auto_loop.operations[op_id_success].status, OperationStatus.SUCCESS)
        
        # Test failing operation
        op_id_fail = self.auto_loop.add_operation(
            "test_fail",
            "Failing Operation",
            failing_operation,
            max_retries=1
        )
        
        success = self.auto_loop.execute_operation(op_id_fail)
        self.assertFalse(success)
        self.assertEqual(self.auto_loop.operations[op_id_fail].status, OperationStatus.RETRYING)
    
    def test_mode_switching(self):
        """Test operation mode switching"""
        self.assertEqual(self.auto_loop.operation_mode, OperationMode.MANUAL)
        
        self.auto_loop.set_operation_mode(OperationMode.AUTO)
        self.assertEqual(self.auto_loop.operation_mode, OperationMode.AUTO)
        self.assertTrue(self.auto_loop.is_running)
        
        self.auto_loop.set_operation_mode(OperationMode.MANUAL)
        self.assertEqual(self.auto_loop.operation_mode, OperationMode.MANUAL)
        self.assertFalse(self.auto_loop.is_running)
    
    def test_statistics_tracking(self):
        """Test statistics tracking"""
        def test_operation():
            return True
        
        # Add and execute operation
        op_id = self.auto_loop.add_operation(
            "stats_test",
            "Statistics Test",
            test_operation
        )
        
        self.auto_loop.execute_operation(op_id)
        
        stats = self.auto_loop.get_statistics()
        self.assertEqual(stats["total_operations"], 1)
        self.assertEqual(stats["successful_operations"], 1)
        self.assertEqual(stats["operation_mode"], "manual")


class TestEnhancedSelfUpgradeSystem(unittest.TestCase):
    """Test enhanced self-upgrade system"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.mock_agent = Mock()
        self.upgrade_system = EnhancedSelfUpgradeSystem(
            self.mock_agent,
            config_file=os.path.join(self.temp_dir, "upgrade_config.json")
        )
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir)
    
    def test_code_validation(self):
        """Test enhanced code validation"""
        # Test valid code
        valid_code = """
import sys
import os

def test_function():
    return "Hello, World!"

class TestClass:
    def __init__(self):
        self.value = 42
"""
        
        result = self.upgrade_system.enhanced_code_validation(valid_code)
        self.assertTrue(result["syntax_valid"])
        self.assertTrue(result["valid"])
        
        # Test dangerous code
        dangerous_code = """
import os
os.system("rm -rf /")
"""
        
        result = self.upgrade_system.enhanced_code_validation(dangerous_code)
        self.assertTrue(result["syntax_valid"])
        self.assertFalse(result["valid"])
        self.assertGreater(len(result["issues"]), 0)
    
    def test_integrity_verification(self):
        """Test code integrity verification"""
        test_code = "print('Hello, World!')"
        expected_hash = "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3"
        
        # Test with correct hash
        result = self.upgrade_system.verify_code_integrity(test_code, expected_hash)
        self.assertTrue(result["valid"])
        
        # Test with incorrect hash
        result = self.upgrade_system.verify_code_integrity(test_code, "wrong_hash")
        self.assertFalse(result["valid"])
    
    def test_backup_creation(self):
        """Test backup creation and management"""
        # Create a temporary file to backup
        test_file = os.path.join(self.temp_dir, "test_agent.py")
        with open(test_file, 'w') as f:
            f.write("# Test agent code")
        
        # Mock __file__ to point to our test file
        with patch('improved_self_upgrade_system.__file__', test_file):
            backup_file = self.upgrade_system.create_backup()
            self.assertTrue(os.path.exists(backup_file))
            
            # Check metadata file
            metadata_file = backup_file.replace('.py', '_metadata.json')
            self.assertTrue(os.path.exists(metadata_file))
            
            with open(metadata_file, 'r') as f:
                metadata = json.load(f)
            
            self.assertIn("version", metadata)
            self.assertIn("timestamp", metadata)


class TestIntegration(unittest.TestCase):
    """Integration tests for the complete enhanced system"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir)
    
    def test_component_integration(self):
        """Test integration between different enhanced components"""
        # Initialize components
        config_manager = EnhancedConfigManager(self.temp_dir)
        error_handler = EnhancedErrorHandler()
        auto_loop = AutoLoopSystem()
        
        # Test configuration affects other components
        config_manager.auto_loop.enabled = True
        config_manager.auto_loop.mode = "auto"
        
        # Test error handling integration
        def failing_operation():
            raise ConnectionError("Test connection error")
        
        op_id = auto_loop.add_operation(
            "integration_test",
            "Integration Test Operation",
            failing_operation
        )
        
        # Execute and verify error handling
        success = auto_loop.execute_operation(op_id)
        self.assertFalse(success)
        
        # Verify operation is in retry state
        operation = auto_loop.operations[op_id]
        self.assertEqual(operation.status, OperationStatus.RETRYING)
    
    def test_configuration_persistence_integration(self):
        """Test configuration persistence across component restarts"""
        # Create and configure components
        config_manager = EnhancedConfigManager(self.temp_dir)
        config_manager.ollama.base_url = "http://integration-test:11434"
        config_manager.auto_loop.max_retries = 5
        config_manager.save_all_configs()
        
        # Restart components
        new_config_manager = EnhancedConfigManager(self.temp_dir)
        
        # Verify persistence
        self.assertEqual(new_config_manager.ollama.base_url, "http://integration-test:11434")
        self.assertEqual(new_config_manager.auto_loop.max_retries, 5)


def run_comprehensive_tests():
    """Run all comprehensive tests"""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestEnhancedConfigManager,
        TestEnhancedConnectionHandler,
        TestEnhancedErrorHandler,
        TestAutoLoopSystem,
        TestEnhancedSelfUpgradeSystem,
        TestOrchestrator,
        TestAgentBase,
        TestErrorLogger,
        TestNexusLight,
        TestRAGDatabase,
        TestContext7MCPServer,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\n{'='*60}")
    print(f"Test Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    print(f"{'='*60}")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
    sys.exit(0 if success else 1)
class TestRAGDatabase(unittest.TestCase):
    """Test cases for the RAGDatabase class."""

    def setUp(self):
        self.rag_db = RAGDatabase()

    def test_add_document(self):
        """Test adding a document to the RAG database."""
        doc_id = "doc1"
        content = "This is a test document about AI."
        self.rag_db.add_document(doc_id, content)
        self.assertIn(doc_id, self.rag_db.data_store)
        self.assertEqual(self.rag_db.data_store[doc_id], content)

    def test_retrieve_information_found(self):
        """Test retrieving information when relevant data exists."""
        self.rag_db.add_document("doc1", "The quick brown fox jumps over the lazy dog.")
        self.rag_db.add_document("doc2", "AI is transforming the world.")
        self.rag_db.add_document("doc3", "Python is a popular programming language.")

        query = "AI"
        result = self.rag_db.retrieve_information(query)
        self.assertIn("AI is transforming the world.", result)
        self.assertNotIn("The quick brown fox jumps over the lazy dog.", result)

        query_case_insensitive = "python"
        result_case_insensitive = self.rag_db.retrieve_information(query_case_insensitive)
        self.assertIn("Python is a popular programming language.", result_case_insensitive)

    def test_retrieve_information_not_found(self):
        """Test retrieving information when no relevant data exists."""
        self.rag_db.add_document("doc1", "Some unrelated content.")
        query = "nonexistent"
        result = self.rag_db.retrieve_information(query)
        self.assertEqual(result, "No relevant information found.")

    def test_update_document(self):
        """Test updating an existing document."""
        doc_id = "doc_to_update"
        initial_content = "Initial content."
        updated_content = "Updated content for the document."

        self.rag_db.add_document(doc_id, initial_content)
        self.assertEqual(self.rag_db.data_store[doc_id], initial_content)

        self.rag_db.update_document(doc_id, updated_content)
        self.assertEqual(self.rag_db.data_store[doc_id], updated_content)

        # Test updating a non-existent document (should not raise error, just print)
        with patch('builtins.print') as mock_print:
            self.rag_db.update_document("non_existent_doc", "new content")
            mock_print.assert_called_with("RAGDatabase: Document 'non_existent_doc' not found for update.")

    def test_delete_document(self):
        """Test deleting a document."""
        doc_id = "doc_to_delete"
        self.rag_db.add_document(doc_id, "Content to be deleted.")
        self.assertIn(doc_id, self.rag_db.data_store)

        self.rag_db.delete_document(doc_id)
        self.assertNotIn(doc_id, self.rag_db.data_store)

        # Test deleting a non-existent document (should not raise error, just print)
        with patch('builtins.print') as mock_print:
            self.rag_db.delete_document("non_existent_doc")
            mock_print.assert_called_with("RAGDatabase: Document 'non_existent_doc' not found for deletion.")


class TestContext7MCPServer(unittest.TestCase):
    """Test cases for the Context7MCPServer class."""

    def setUp(self):
        self.mcp_server = Context7MCPServer()

    def test_initialization(self):
        """Test Context7MCPServer initialization."""
        self.assertEqual(self.mcp_server.context_store, {})

    def test_update_context(self):
        """Test updating context for an agent."""
        agent_name = "SubAgentAlpha"
        context1 = "SubAgentAlpha started its task."
        context2 = "SubAgentAlpha processed data X."

        self.mcp_server.update_context(agent_name, context1)
        self.assertIn(agent_name, self.mcp_server.context_store)
        self.assertEqual(self.mcp_server.context_store[agent_name], [context1])

        self.mcp_server.update_context(agent_name, context2)
        self.assertEqual(self.mcp_server.context_store[agent_name], [context1, context2])

    def test_get_context_found(self):
        """Test retrieving context when relevant data exists."""
        agent_name = "SubAgentBeta"
        self.mcp_server.update_context(agent_name, "Beta is working on network optimization.")
        self.mcp_server.update_context(agent_name, "Network latency is high.")
        self.mcp_server.update_context(agent_name, "Optimizing routing tables.")

        query = "network"
        result = self.mcp_server.get_context(agent_name, query)
        self.assertIn("Beta is working on network optimization.", result)
        self.assertIn("Network latency is high.", result)
        self.assertNotIn("Optimizing routing tables.", result) # Should not contain this as query is "network"

        query_case_insensitive = "latency"
        result_case_insensitive = self.mcp_server.get_context(agent_name, query_case_insensitive)
        self.assertIn("Network latency is high.", result_case_insensitive)

    def test_get_context_not_found_for_agent(self):
        """Test retrieving context for an agent with no stored context."""
        query = "any"
        result = self.mcp_server.get_context("NonExistentAgent", query)
        self.assertEqual(result, "No relevant context found for 'NonExistentAgent'.")

    def test_get_context_no_relevant_info(self):
        """Test retrieving context when agent has context but no relevant info for query."""
        agent_name = "SubAgentGamma"
        self.mcp_server.update_context(agent_name, "Gamma is analyzing logs.")
        query = "database"
        result = self.mcp_server.get_context(agent_name, query)
        self.assertEqual(result, f"No relevant context found for '{agent_name}'.")

    def test_clear_context(self):
        """Test clearing context for an agent."""
        agent_name = "SubAgentDelta"
        self.mcp_server.update_context(agent_name, "Some context data.")
        self.assertIn(agent_name, self.mcp_server.context_store)

        self.mcp_server.clear_context(agent_name)
        self.assertNotIn(agent_name, self.mcp_server.context_store)

        # Test clearing context for a non-existent agent (should not raise error, just print)
        with patch('builtins.print') as mock_print:
            self.mcp_server.clear_context("NonExistentAgent")
            mock_print.assert_called_with("Context7MCPServer: No context found for agent 'NonExistentAgent' to clear.")

