#!/usr/bin/env python3
"""
Enhanced Configuration Management System with persistence and validation
"""

import os
import json
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from pathlib import Path
import configparser


@dataclass
class OllamaConfig:
    base_url: str = "http://localhost:11434"
    timeout_connect: float = 5.0
    timeout_read: float = 30.0
    timeout_chat: float = 120.0
    timeout_model_download: float = 300.0
    max_retries: int = 3
    backoff_factor: float = 0.3


@dataclass
class UIConfig:
    window_width: int = 1200
    window_height: int = 800
    window_x: int = 100
    window_y: int = 100
    font_family: str = "Consolas"
    font_size: int = 10
    theme: str = "Fusion"
    save_chat_history: bool = True
    auto_save_interval: int = 300  # seconds


@dataclass
class AutoLoopConfig:
    enabled: bool = False
    mode: str = "manual"  # manual, auto, smart_auto
    max_retries: int = 3
    base_delay: float = 1.0
    backoff_multiplier: float = 1.5
    max_delay: float = 60.0
    operation_timeout: float = 300.0
    cleanup_interval: int = 3600
    max_operation_history: int = 100


@dataclass
class UpgradeConfig:
    auto_check: bool = False
    check_interval: int = 3600  # seconds
    backup_enabled: bool = True
    max_backups: int = 5
    upgrade_url: str = ""
    public_key: str = ""
    rollback_timeout: int = 300


@dataclass
class LoggingConfig:
    level: str = "INFO"
    file_enabled: bool = True
    file_path: str = "agent.log"
    max_file_size: int = 10485760  # 10MB
    backup_count: int = 5
    console_enabled: bool = True


class EnhancedConfigManager:
    """Enhanced configuration manager with validation and persistence"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        # Configuration files
        self.main_config_file = self.config_dir / "agent_config.json"
        self.user_config_file = self.config_dir / "user_config.ini"
        
        # Configuration objects
        self.ollama = OllamaConfig()
        self.ui = UIConfig()
        self.auto_loop = AutoLoopConfig()
        self.upgrade = UpgradeConfig()
        self.logging = LoggingConfig()
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Load configurations
        self.load_all_configs()
    
    def load_all_configs(self):
        """Load all configuration files"""
        try:
            self._load_main_config()
            self._load_user_config()
            self._load_environment_variables()
            self.logger.info("Configuration loaded successfully")
        except Exception as e:
            self.logger.error(f"Error loading configuration: {e}")
    
    def _load_main_config(self):
        """Load main JSON configuration file"""
        if self.main_config_file.exists():
            try:
                with open(self.main_config_file, 'r') as f:
                    config_data = json.load(f)
                
                # Update configuration objects
                if "ollama" in config_data:
                    self.ollama = OllamaConfig(**{**asdict(self.ollama), **config_data["ollama"]})
                
                if "ui" in config_data:
                    self.ui = UIConfig(**{**asdict(self.ui), **config_data["ui"]})
                
                if "auto_loop" in config_data:
                    self.auto_loop = AutoLoopConfig(**{**asdict(self.auto_loop), **config_data["auto_loop"]})
                
                if "upgrade" in config_data:
                    self.upgrade = UpgradeConfig(**{**asdict(self.upgrade), **config_data["upgrade"]})
                
                if "logging" in config_data:
                    self.logging = LoggingConfig(**{**asdict(self.logging), **config_data["logging"]})
                    
            except Exception as e:
                self.logger.warning(f"Error loading main config: {e}")
    
    def _load_user_config(self):
        """Load user-specific INI configuration file"""
        if self.user_config_file.exists():
            try:
                config = configparser.ConfigParser()
                config.read(self.user_config_file)
                
                # Load UI preferences
                if "UI" in config:
                    ui_section = config["UI"]
                    self.ui.window_width = ui_section.getint("window_width", self.ui.window_width)
                    self.ui.window_height = ui_section.getint("window_height", self.ui.window_height)
                    self.ui.window_x = ui_section.getint("window_x", self.ui.window_x)
                    self.ui.window_y = ui_section.getint("window_y", self.ui.window_y)
                    self.ui.font_family = ui_section.get("font_family", self.ui.font_family)
                    self.ui.font_size = ui_section.getint("font_size", self.ui.font_size)
                    self.ui.theme = ui_section.get("theme", self.ui.theme)
                
                # Load Ollama preferences
                if "OLLAMA" in config:
                    ollama_section = config["OLLAMA"]
                    self.ollama.base_url = ollama_section.get("base_url", self.ollama.base_url)
                    
            except Exception as e:
                self.logger.warning(f"Error loading user config: {e}")
    
    def _load_environment_variables(self):
        """Load configuration from environment variables"""
        try:
            # Ollama configuration
            if "OLLAMA_BASE_URL" in os.environ:
                self.ollama.base_url = os.environ["OLLAMA_BASE_URL"]
            
            if "OLLAMA_TIMEOUT" in os.environ:
                timeout = float(os.environ["OLLAMA_TIMEOUT"])
                self.ollama.timeout_chat = timeout
            
            # Auto-loop configuration
            if "AUTO_LOOP_ENABLED" in os.environ:
                self.auto_loop.enabled = os.environ["AUTO_LOOP_ENABLED"].lower() == "true"
            
            if "AUTO_LOOP_MODE" in os.environ:
                self.auto_loop.mode = os.environ["AUTO_LOOP_MODE"]
            
            # Logging configuration
            if "LOG_LEVEL" in os.environ:
                self.logging.level = os.environ["LOG_LEVEL"].upper()
            
            if "LOG_FILE" in os.environ:
                self.logging.file_path = os.environ["LOG_FILE"]
                
        except Exception as e:
            self.logger.warning(f"Error loading environment variables: {e}")
    
    def save_all_configs(self):
        """Save all configurations to files"""
        try:
            self._save_main_config()
            self._save_user_config()
            self.logger.info("Configuration saved successfully")
        except Exception as e:
            self.logger.error(f"Error saving configuration: {e}")
    
    def _save_main_config(self):
        """Save main JSON configuration file"""
        try:
            config_data = {
                "ollama": asdict(self.ollama),
                "ui": asdict(self.ui),
                "auto_loop": asdict(self.auto_loop),
                "upgrade": asdict(self.upgrade),
                "logging": asdict(self.logging)
            }
            
            with open(self.main_config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Error saving main config: {e}")
    
    def _save_user_config(self):
        """Save user-specific INI configuration file"""
        try:
            config = configparser.ConfigParser()
            
            # UI section
            config["UI"] = {
                "window_width": str(self.ui.window_width),
                "window_height": str(self.ui.window_height),
                "window_x": str(self.ui.window_x),
                "window_y": str(self.ui.window_y),
                "font_family": self.ui.font_family,
                "font_size": str(self.ui.font_size),
                "theme": self.ui.theme
            }
            
            # Ollama section
            config["OLLAMA"] = {
                "base_url": self.ollama.base_url
            }
            
            with open(self.user_config_file, 'w') as f:
                config.write(f)
                
        except Exception as e:
            self.logger.error(f"Error saving user config: {e}")
    
    def validate_config(self) -> List[str]:
        """Validate configuration and return list of issues"""
        issues = []
        
        # Validate Ollama configuration
        if not self.ollama.base_url.startswith(("http://", "https://")):
            issues.append("Ollama base URL must start with http:// or https://")
        
        if self.ollama.timeout_connect <= 0:
            issues.append("Ollama connect timeout must be positive")
        
        if self.ollama.max_retries < 0:
            issues.append("Ollama max retries must be non-negative")
        
        # Validate UI configuration
        if self.ui.window_width < 800:
            issues.append("Window width should be at least 800 pixels")
        
        if self.ui.window_height < 600:
            issues.append("Window height should be at least 600 pixels")
        
        if self.ui.font_size < 8 or self.ui.font_size > 72:
            issues.append("Font size should be between 8 and 72")
        
        # Validate auto-loop configuration
        if self.auto_loop.mode not in ["manual", "auto", "smart_auto"]:
            issues.append("Auto-loop mode must be 'manual', 'auto', or 'smart_auto'")
        
        if self.auto_loop.max_retries < 1:
            issues.append("Auto-loop max retries must be at least 1")
        
        if self.auto_loop.base_delay <= 0:
            issues.append("Auto-loop base delay must be positive")
        
        # Validate logging configuration
        valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.logging.level not in valid_log_levels:
            issues.append(f"Log level must be one of: {', '.join(valid_log_levels)}")
        
        if self.logging.max_file_size <= 0:
            issues.append("Log file max size must be positive")
        
        return issues
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get a summary of current configuration"""
        return {
            "ollama": {
                "base_url": self.ollama.base_url,
                "chat_timeout": self.ollama.timeout_chat,
                "max_retries": self.ollama.max_retries
            },
            "ui": {
                "window_size": f"{self.ui.window_width}x{self.ui.window_height}",
                "font": f"{self.ui.font_family} {self.ui.font_size}pt",
                "theme": self.ui.theme
            },
            "auto_loop": {
                "enabled": self.auto_loop.enabled,
                "mode": self.auto_loop.mode,
                "max_retries": self.auto_loop.max_retries
            },
            "upgrade": {
                "auto_check": self.upgrade.auto_check,
                "backup_enabled": self.upgrade.backup_enabled
            },
            "logging": {
                "level": self.logging.level,
                "file_enabled": self.logging.file_enabled
            }
        }
    
    def reset_to_defaults(self):
        """Reset all configuration to default values"""
        self.ollama = OllamaConfig()
        self.ui = UIConfig()
        self.auto_loop = AutoLoopConfig()
        self.upgrade = UpgradeConfig()
        self.logging = LoggingConfig()
        self.logger.info("Configuration reset to defaults")
    
    def export_config(self, export_path: str):
        """Export configuration to a file"""
        try:
            config_data = {
                "ollama": asdict(self.ollama),
                "ui": asdict(self.ui),
                "auto_loop": asdict(self.auto_loop),
                "upgrade": asdict(self.upgrade),
                "logging": asdict(self.logging)
            }
            
            with open(export_path, 'w') as f:
                json.dump(config_data, f, indent=2)
            
            self.logger.info(f"Configuration exported to {export_path}")
            
        except Exception as e:
            self.logger.error(f"Error exporting configuration: {e}")
            raise
    
    def import_config(self, import_path: str):
        """Import configuration from a file"""
        try:
            with open(import_path, 'r') as f:
                config_data = json.load(f)
            
            # Validate and update configurations
            if "ollama" in config_data:
                self.ollama = OllamaConfig(**config_data["ollama"])
            
            if "ui" in config_data:
                self.ui = UIConfig(**config_data["ui"])
            
            if "auto_loop" in config_data:
                self.auto_loop = AutoLoopConfig(**config_data["auto_loop"])
            
            if "upgrade" in config_data:
                self.upgrade = UpgradeConfig(**config_data["upgrade"])
            
            if "logging" in config_data:
                self.logging = LoggingConfig(**config_data["logging"])
            
            # Validate imported configuration
            issues = self.validate_config()
            if issues:
                self.logger.warning(f"Configuration validation issues: {issues}")
            
            self.logger.info(f"Configuration imported from {import_path}")
            
        except Exception as e:
            self.logger.error(f"Error importing configuration: {e}")
            raise
