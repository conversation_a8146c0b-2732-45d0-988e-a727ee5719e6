#!/usr/bin/env python3
"""
Evolutionary Algorithm Discovery System inspired by AlphaEvolve
Evolves and optimizes algorithms for the Self-UpgradingAI system
"""

import random
import json
import time
import hashlib
from typing import Dict, List, Tuple, Optional, Callable, Any
from dataclasses import dataclass, asdict
from enum import Enum
import logging
import ast
import copy


class AlgorithmType(Enum):
    CONNECTION_HANDLER = "connection_handler"
    ERROR_RECOVERY = "error_recovery"
    MODEL_OPTIMIZATION = "model_optimization"
    RETRY_STRATEGY = "retry_strategy"
    RESOURCE_MANAGEMENT = "resource_management"


@dataclass
class Algorithm:
    id: str
    type: AlgorithmType
    code: str
    parameters: Dict[str, Any]
    fitness_score: float
    generation: int
    parent_ids: List[str]
    mutations: List[str]
    performance_metrics: Dict[str, float]
    created_at: float
    tested: bool = False


@dataclass
class EvolutionConfig:
    population_size: int = 20
    mutation_rate: float = 0.3
    crossover_rate: float = 0.7
    elite_ratio: float = 0.2
    max_generations: int = 50
    fitness_threshold: float = 0.9
    diversity_weight: float = 0.1


class EvolutionaryAlgorithmDiscovery:
    """
    Evolutionary system for discovering and optimizing algorithms
    Inspired by AlphaEvolve's approach to algorithm evolution
    """
    
    def __init__(self, config: EvolutionConfig = None):
        self.config = config or EvolutionConfig()
        self.populations: Dict[AlgorithmType, List[Algorithm]] = {}
        self.generation_history: Dict[AlgorithmType, List[Dict]] = {}
        self.best_algorithms: Dict[AlgorithmType, Algorithm] = {}
        
        # Algorithm templates and building blocks
        self.algorithm_templates = self._load_algorithm_templates()
        self.mutation_operators = self._initialize_mutation_operators()
        
        self.logger = logging.getLogger(__name__)
        
    def _load_algorithm_templates(self) -> Dict[AlgorithmType, List[str]]:
        """Load base algorithm templates for different types"""
        return {
            AlgorithmType.CONNECTION_HANDLER: [
                """
def enhanced_connection_handler(base_url, timeout_config):
    import requests
    import time
    
    session = requests.Session()
    retry_count = {retry_count}
    backoff_factor = {backoff_factor}
    
    for attempt in range(retry_count):
        try:
            response = session.get(base_url, timeout=timeout_config['connect'])
            if response.status_code == 200:
                return response
        except Exception as e:
            if attempt < retry_count - 1:
                time.sleep(backoff_factor ** attempt)
            else:
                raise e
    return None
""",
                """
def adaptive_connection_handler(base_url, timeout_config):
    import requests
    import time
    
    session = requests.Session()
    adaptive_timeout = {base_timeout}
    success_rate = 1.0
    
    while True:
        try:
            adjusted_timeout = adaptive_timeout * (2.0 - success_rate)
            response = session.get(base_url, timeout=adjusted_timeout)
            success_rate = min(1.0, success_rate + {learning_rate})
            return response
        except Exception as e:
            success_rate = max(0.1, success_rate - {penalty_rate})
            adaptive_timeout = min({max_timeout}, adaptive_timeout * {timeout_multiplier})
            time.sleep({base_delay})
"""
            ],
            
            AlgorithmType.ERROR_RECOVERY: [
                """
def intelligent_error_recovery(error, context):
    recovery_strategies = {recovery_strategies}
    
    for strategy in recovery_strategies:
        try:
            if strategy['condition'](error, context):
                result = strategy['action'](error, context)
                if result:
                    return True
        except Exception:
            continue
    return False
""",
                """
def adaptive_error_recovery(error, context, history):
    success_rates = history.get('success_rates', {{}})
    
    # Sort strategies by success rate
    strategies = sorted({strategies}, 
                       key=lambda s: success_rates.get(s['name'], 0.5), 
                       reverse=True)
    
    for strategy in strategies:
        try:
            if strategy['weight'] > {threshold}:
                result = strategy['execute'](error, context)
                if result:
                    success_rates[strategy['name']] = min(1.0, 
                        success_rates.get(strategy['name'], 0.5) + {learning_rate})
                    return True
                else:
                    success_rates[strategy['name']] = max(0.0,
                        success_rates.get(strategy['name'], 0.5) - {penalty_rate})
        except Exception:
            continue
    return False
"""
            ],
            
            AlgorithmType.RETRY_STRATEGY: [
                """
def exponential_backoff_retry(operation, max_retries={max_retries}):
    import time
    import random
    
    base_delay = {base_delay}
    max_delay = {max_delay}
    jitter_factor = {jitter_factor}
    
    for attempt in range(max_retries):
        try:
            return operation()
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            
            delay = min(max_delay, base_delay * (2 ** attempt))
            jitter = random.uniform(0, delay * jitter_factor)
            time.sleep(delay + jitter)
    
    return None
""",
                """
def adaptive_retry_strategy(operation, context):
    import time
    
    success_history = context.get('success_history', [])
    recent_success_rate = sum(success_history[-{window_size}:]) / min(len(success_history), {window_size})
    
    # Adapt retry parameters based on recent success
    max_retries = int({base_retries} * (2.0 - recent_success_rate))
    base_delay = {base_delay} * (2.0 - recent_success_rate)
    
    for attempt in range(max_retries):
        try:
            result = operation()
            success_history.append(1)
            return result
        except Exception as e:
            success_history.append(0)
            if attempt == max_retries - 1:
                raise e
            time.sleep(base_delay * (attempt + 1))
    
    return None
"""
            ]
        }
    
    def _initialize_mutation_operators(self) -> List[Callable]:
        """Initialize mutation operators for algorithm evolution"""
        return [
            self._mutate_parameters,
            self._mutate_structure,
            self._mutate_logic,
            self._add_optimization,
            self._remove_redundancy
        ]
    
    def initialize_population(self, algorithm_type: AlgorithmType, 
                            problem_context: Dict[str, Any] = None) -> List[Algorithm]:
        """Initialize population for a specific algorithm type"""
        population = []
        templates = self.algorithm_templates.get(algorithm_type, [])
        
        if not templates:
            self.logger.warning(f"No templates found for {algorithm_type}")
            return population
        
        for i in range(self.config.population_size):
            # Select random template
            template = random.choice(templates)
            
            # Generate random parameters
            parameters = self._generate_random_parameters(algorithm_type)
            
            # Fill template with parameters
            code = template.format(**parameters)
            
            # Create algorithm
            algorithm = Algorithm(
                id=self._generate_algorithm_id(code),
                type=algorithm_type,
                code=code,
                parameters=parameters,
                fitness_score=0.0,
                generation=0,
                parent_ids=[],
                mutations=[],
                performance_metrics={},
                created_at=time.time()
            )
            
            population.append(algorithm)
        
        self.populations[algorithm_type] = population
        self.generation_history[algorithm_type] = []
        
        self.logger.info(f"Initialized population of {len(population)} algorithms for {algorithm_type}")
        return population
    
    def _generate_random_parameters(self, algorithm_type: AlgorithmType) -> Dict[str, Any]:
        """Generate random parameters for algorithm templates"""
        base_params = {
            AlgorithmType.CONNECTION_HANDLER: {
                'retry_count': random.randint(2, 5),
                'backoff_factor': round(random.uniform(1.2, 2.0), 2),
                'base_timeout': round(random.uniform(5.0, 30.0), 1),
                'max_timeout': round(random.uniform(60.0, 300.0), 1),
                'learning_rate': round(random.uniform(0.01, 0.1), 3),
                'penalty_rate': round(random.uniform(0.05, 0.2), 3),
                'timeout_multiplier': round(random.uniform(1.1, 1.5), 2),
                'base_delay': round(random.uniform(0.5, 2.0), 1)
            },
            AlgorithmType.ERROR_RECOVERY: {
                'recovery_strategies': '[]',  # Placeholder
                'strategies': '[]',  # Placeholder
                'threshold': round(random.uniform(0.3, 0.8), 2),
                'learning_rate': round(random.uniform(0.01, 0.1), 3),
                'penalty_rate': round(random.uniform(0.05, 0.2), 3)
            },
            AlgorithmType.RETRY_STRATEGY: {
                'max_retries': random.randint(3, 8),
                'base_delay': round(random.uniform(0.5, 3.0), 1),
                'max_delay': round(random.uniform(30.0, 120.0), 1),
                'jitter_factor': round(random.uniform(0.1, 0.5), 2),
                'window_size': random.randint(5, 20),
                'base_retries': random.randint(3, 6)
            }
        }
        
        return base_params.get(algorithm_type, {})
    
    def _generate_algorithm_id(self, code: str) -> str:
        """Generate unique ID for algorithm based on code hash"""
        return hashlib.md5(code.encode()).hexdigest()[:12]
    
    def evaluate_fitness(self, algorithm: Algorithm, 
                        test_cases: List[Dict[str, Any]]) -> float:
        """Evaluate algorithm fitness using test cases"""
        try:
            # Compile and test the algorithm
            compiled_code = compile(algorithm.code, '<string>', 'exec')
            
            total_score = 0.0
            performance_metrics = {}
            
            for test_case in test_cases:
                # Create execution environment
                exec_globals = {'__builtins__': __builtins__}
                exec(compiled_code, exec_globals)
                
                # Extract the main function
                func_name = self._extract_function_name(algorithm.code)
                if func_name not in exec_globals:
                    continue
                
                func = exec_globals[func_name]
                
                # Run test case
                start_time = time.time()
                try:
                    result = func(**test_case['inputs'])
                    execution_time = time.time() - start_time
                    
                    # Evaluate result
                    score = self._evaluate_test_result(result, test_case['expected'], execution_time)
                    total_score += score
                    
                    # Track performance metrics
                    performance_metrics[f"test_{test_case.get('id', 'unknown')}"] = {
                        'score': score,
                        'execution_time': execution_time,
                        'success': score > 0.5
                    }
                    
                except Exception as e:
                    self.logger.warning(f"Test case failed: {e}")
                    performance_metrics[f"test_{test_case.get('id', 'unknown')}"] = {
                        'score': 0.0,
                        'execution_time': time.time() - start_time,
                        'success': False,
                        'error': str(e)
                    }
            
            # Calculate average fitness
            fitness = total_score / len(test_cases) if test_cases else 0.0
            
            # Update algorithm
            algorithm.fitness_score = fitness
            algorithm.performance_metrics = performance_metrics
            algorithm.tested = True
            
            return fitness
            
        except Exception as e:
            self.logger.error(f"Fitness evaluation failed: {e}")
            algorithm.fitness_score = 0.0
            algorithm.tested = True
            return 0.0
    
    def _extract_function_name(self, code: str) -> str:
        """Extract main function name from code"""
        try:
            tree = ast.parse(code)
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    return node.name
        except:
            pass
        return ""
    
    def _evaluate_test_result(self, result: Any, expected: Any, execution_time: float) -> float:
        """Evaluate test result and return score"""
        # Basic scoring logic
        correctness_score = 1.0 if result == expected else 0.0
        
        # Performance penalty for slow execution
        time_penalty = min(0.5, execution_time / 10.0)  # Penalty for >10s execution
        performance_score = max(0.0, 1.0 - time_penalty)
        
        # Combined score
        return (correctness_score * 0.7) + (performance_score * 0.3)
    
    def evolve_generation(self, algorithm_type: AlgorithmType, 
                         test_cases: List[Dict[str, Any]]) -> List[Algorithm]:
        """Evolve one generation of algorithms"""
        population = self.populations.get(algorithm_type, [])
        if not population:
            self.logger.error(f"No population found for {algorithm_type}")
            return []
        
        # Evaluate fitness for untested algorithms
        for algorithm in population:
            if not algorithm.tested:
                self.evaluate_fitness(algorithm, test_cases)
        
        # Sort by fitness
        population.sort(key=lambda a: a.fitness_score, reverse=True)
        
        # Record generation statistics
        generation_stats = {
            'generation': population[0].generation + 1,
            'best_fitness': population[0].fitness_score,
            'average_fitness': sum(a.fitness_score for a in population) / len(population),
            'diversity': self._calculate_diversity(population)
        }
        
        self.generation_history[algorithm_type].append(generation_stats)
        
        # Select elite algorithms
        elite_count = int(len(population) * self.config.elite_ratio)
        elite_algorithms = population[:elite_count]
        
        # Generate new population
        new_population = elite_algorithms.copy()
        
        while len(new_population) < self.config.population_size:
            if random.random() < self.config.crossover_rate:
                # Crossover
                parent1, parent2 = self._select_parents(population)
                child = self._crossover(parent1, parent2)
            else:
                # Mutation only
                parent = self._select_parent(population)
                child = self._mutate(parent)
            
            child.generation = population[0].generation + 1
            child.tested = False
            new_population.append(child)
        
        self.populations[algorithm_type] = new_population
        
        # Update best algorithm
        if population[0].fitness_score > self.best_algorithms.get(algorithm_type, Algorithm("", algorithm_type, "", {}, 0.0, 0, [], [], {}, 0.0)).fitness_score:
            self.best_algorithms[algorithm_type] = population[0]
        
        self.logger.info(f"Generation {generation_stats['generation']} - Best: {generation_stats['best_fitness']:.3f}, Avg: {generation_stats['average_fitness']:.3f}")
        
        return new_population
    
    def _calculate_diversity(self, population: List[Algorithm]) -> float:
        """Calculate population diversity"""
        if len(population) < 2:
            return 0.0
        
        # Simple diversity measure based on code differences
        total_differences = 0
        comparisons = 0
        
        for i in range(len(population)):
            for j in range(i + 1, len(population)):
                # Calculate code similarity
                similarity = self._calculate_code_similarity(population[i].code, population[j].code)
                total_differences += (1.0 - similarity)
                comparisons += 1
        
        return total_differences / comparisons if comparisons > 0 else 0.0
    
    def _calculate_code_similarity(self, code1: str, code2: str) -> float:
        """Calculate similarity between two code strings"""
        # Simple similarity based on common lines
        lines1 = set(code1.strip().split('\n'))
        lines2 = set(code2.strip().split('\n'))
        
        if not lines1 and not lines2:
            return 1.0
        
        intersection = len(lines1.intersection(lines2))
        union = len(lines1.union(lines2))
        
        return intersection / union if union > 0 else 0.0
    
    def _select_parents(self, population: List[Algorithm]) -> Tuple[Algorithm, Algorithm]:
        """Select two parents for crossover"""
        parent1 = self._select_parent(population)
        parent2 = self._select_parent(population)
        return parent1, parent2
    
    def _select_parent(self, population: List[Algorithm]) -> Algorithm:
        """Select a parent using tournament selection"""
        tournament_size = min(3, len(population))
        tournament = random.sample(population, tournament_size)
        return max(tournament, key=lambda a: a.fitness_score)
    
    def _crossover(self, parent1: Algorithm, parent2: Algorithm) -> Algorithm:
        """Create child algorithm through crossover"""
        # Simple parameter crossover
        child_parameters = {}
        for key in parent1.parameters:
            if key in parent2.parameters:
                if random.random() < 0.5:
                    child_parameters[key] = parent1.parameters[key]
                else:
                    child_parameters[key] = parent2.parameters[key]
            else:
                child_parameters[key] = parent1.parameters[key]
        
        # Use parent1's template but with mixed parameters
        template = self._extract_template_from_code(parent1.code)
        child_code = template.format(**child_parameters)
        
        child = Algorithm(
            id=self._generate_algorithm_id(child_code),
            type=parent1.type,
            code=child_code,
            parameters=child_parameters,
            fitness_score=0.0,
            generation=0,
            parent_ids=[parent1.id, parent2.id],
            mutations=['crossover'],
            performance_metrics={},
            created_at=time.time()
        )
        
        return child
    
    def _mutate(self, parent: Algorithm) -> Algorithm:
        """Create child algorithm through mutation"""
        child = copy.deepcopy(parent)
        child.id = self._generate_algorithm_id(child.code + str(time.time()))
        child.parent_ids = [parent.id]
        child.mutations = []
        child.fitness_score = 0.0
        child.tested = False
        child.created_at = time.time()
        
        # Apply random mutations
        mutation_count = random.randint(1, 3)
        for _ in range(mutation_count):
            mutation_op = random.choice(self.mutation_operators)
            mutation_op(child)
        
        return child
    
    def _mutate_parameters(self, algorithm: Algorithm):
        """Mutate algorithm parameters"""
        if not algorithm.parameters:
            return
        
        param_key = random.choice(list(algorithm.parameters.keys()))
        current_value = algorithm.parameters[param_key]
        
        if isinstance(current_value, (int, float)):
            # Gaussian mutation
            mutation_strength = 0.1
            if isinstance(current_value, int):
                new_value = max(1, int(current_value + random.gauss(0, current_value * mutation_strength)))
            else:
                new_value = max(0.01, current_value + random.gauss(0, current_value * mutation_strength))
            
            algorithm.parameters[param_key] = new_value
            algorithm.mutations.append(f"mutate_param_{param_key}")
            
            # Regenerate code with new parameters
            template = self._extract_template_from_code(algorithm.code)
            algorithm.code = template.format(**algorithm.parameters)
    
    def _mutate_structure(self, algorithm: Algorithm):
        """Mutate algorithm structure"""
        algorithm.mutations.append("mutate_structure")
        # Placeholder for structural mutations
    
    def _mutate_logic(self, algorithm: Algorithm):
        """Mutate algorithm logic"""
        algorithm.mutations.append("mutate_logic")
        # Placeholder for logic mutations
    
    def _add_optimization(self, algorithm: Algorithm):
        """Add optimization to algorithm"""
        algorithm.mutations.append("add_optimization")
        # Placeholder for optimization additions
    
    def _remove_redundancy(self, algorithm: Algorithm):
        """Remove redundancy from algorithm"""
        algorithm.mutations.append("remove_redundancy")
        # Placeholder for redundancy removal
    
    def _extract_template_from_code(self, code: str) -> str:
        """Extract template pattern from code"""
        # Simple approach: replace numeric values with placeholders
        import re
        
        # Replace numbers with placeholders
        template = re.sub(r'\b\d+\.?\d*\b', lambda m: '{' + f'param_{hash(m.group()) % 1000}' + '}', code)
        return template
    
    def get_best_algorithm(self, algorithm_type: AlgorithmType) -> Optional[Algorithm]:
        """Get the best algorithm for a specific type"""
        return self.best_algorithms.get(algorithm_type)
    
    def export_algorithm(self, algorithm: Algorithm, file_path: str):
        """Export algorithm to file"""
        try:
            with open(file_path, 'w') as f:
                json.dump(asdict(algorithm), f, indent=2, default=str)
            self.logger.info(f"Algorithm exported to {file_path}")
        except Exception as e:
            self.logger.error(f"Failed to export algorithm: {e}")
    
    def import_algorithm(self, file_path: str) -> Optional[Algorithm]:
        """Import algorithm from file"""
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            algorithm = Algorithm(**data)
            self.logger.info(f"Algorithm imported from {file_path}")
            return algorithm
        except Exception as e:
            self.logger.error(f"Failed to import algorithm: {e}")
            return None
