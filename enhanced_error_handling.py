#!/usr/bin/env python3
"""
Enhanced Error Handling and Logging System
"""

import logging
import logging.handlers
import traceback
import sys
import os
import json
from datetime import datetime
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
from dataclasses import dataclass
from functools import wraps
import threading


class ErrorSeverity(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    CONNECTION = "connection"
    VALIDATION = "validation"
    UPGRADE = "upgrade"
    MODEL = "model"
    UI = "ui"
    SYSTEM = "system"
    UNKNOWN = "unknown"


@dataclass
class ErrorInfo:
    timestamp: datetime
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    exception_type: str
    traceback_info: str
    context: Dict[str, Any]
    recovery_attempted: bool = False
    recovery_successful: bool = False


class EnhancedErrorHandler:
    """Enhanced error handler with categorization, recovery, and reporting"""
    
    def __init__(self, log_file: str = "agent_errors.log", max_errors: int = 1000):
        self.log_file = log_file
        self.max_errors = max_errors
        self.errors: List[ErrorInfo] = []
        self.error_counts: Dict[str, int] = {}
        self.recovery_strategies: Dict[ErrorCategory, List[Callable]] = {}
        
        # Thread safety
        self.lock = threading.Lock()
        
        # Setup logging
        self.setup_logging()
        
        # Register default recovery strategies
        self._register_default_recovery_strategies()
    
    def setup_logging(self):
        """Setup enhanced logging configuration"""
        # Create logger
        self.logger = logging.getLogger("enhanced_error_handler")
        self.logger.setLevel(logging.DEBUG)
        
        # Remove existing handlers
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # File handler with rotation
        file_handler = logging.handlers.RotatingFileHandler(
            self.log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        file_handler.setLevel(logging.DEBUG)
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # Add handlers
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def _register_default_recovery_strategies(self):
        """Register default recovery strategies for different error categories"""
        
        # Connection recovery strategies
        self.recovery_strategies[ErrorCategory.CONNECTION] = [
            self._retry_connection,
            self._reset_connection_pool,
            self._fallback_to_local_mode
        ]
        
        # Model recovery strategies
        self.recovery_strategies[ErrorCategory.MODEL] = [
            self._reload_model,
            self._switch_to_fallback_model,
            self._clear_model_cache
        ]
        
        # Validation recovery strategies
        self.recovery_strategies[ErrorCategory.VALIDATION] = [
            self._sanitize_input,
            self._use_default_values
        ]
        
        # UI recovery strategies
        self.recovery_strategies[ErrorCategory.UI] = [
            self._reset_ui_state,
            self._reload_ui_components
        ]
    
    def handle_error(self, exception: Exception, category: ErrorCategory = ErrorCategory.UNKNOWN,
                    severity: ErrorSeverity = ErrorSeverity.MEDIUM, context: Dict[str, Any] = None,
                    attempt_recovery: bool = True) -> bool:
        """Handle an error with categorization and optional recovery"""
        
        if context is None:
            context = {}
        
        # Create error info
        error_info = ErrorInfo(
            timestamp=datetime.now(),
            category=category,
            severity=severity,
            message=str(exception),
            exception_type=type(exception).__name__,
            traceback_info=traceback.format_exc(),
            context=context
        )
        
        # Thread-safe error recording
        with self.lock:
            self.errors.append(error_info)
            
            # Maintain error count
            error_key = f"{category.value}:{type(exception).__name__}"
            self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
            
            # Limit error history
            if len(self.errors) > self.max_errors:
                self.errors = self.errors[-self.max_errors:]
        
        # Log the error
        log_message = f"[{category.value.upper()}] {severity.value.upper()}: {str(exception)}"
        if context:
            log_message += f" | Context: {json.dumps(context, default=str)}"
        
        if severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_message)
        elif severity == ErrorSeverity.HIGH:
            self.logger.error(log_message)
        elif severity == ErrorSeverity.MEDIUM:
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)
        
        # Attempt recovery if requested
        recovery_successful = False
        if attempt_recovery and category in self.recovery_strategies:
            recovery_successful = self._attempt_recovery(error_info)
        
        error_info.recovery_attempted = attempt_recovery
        error_info.recovery_successful = recovery_successful
        
        return recovery_successful
    
    def _attempt_recovery(self, error_info: ErrorInfo) -> bool:
        """Attempt recovery using registered strategies"""
        strategies = self.recovery_strategies.get(error_info.category, [])
        
        for strategy in strategies:
            try:
                self.logger.info(f"Attempting recovery strategy: {strategy.__name__}")
                if strategy(error_info):
                    self.logger.info(f"Recovery successful with strategy: {strategy.__name__}")
                    return True
            except Exception as e:
                self.logger.warning(f"Recovery strategy {strategy.__name__} failed: {e}")
        
        self.logger.warning(f"All recovery strategies failed for {error_info.category.value} error")
        return False
    
    # Default recovery strategies
    def _retry_connection(self, error_info: ErrorInfo) -> bool:
        """Retry connection with exponential backoff"""
        # This would be implemented based on the specific connection type
        self.logger.info("Attempting connection retry...")
        return False  # Placeholder
    
    def _reset_connection_pool(self, error_info: ErrorInfo) -> bool:
        """Reset connection pool"""
        self.logger.info("Resetting connection pool...")
        return False  # Placeholder
    
    def _fallback_to_local_mode(self, error_info: ErrorInfo) -> bool:
        """Fallback to local mode operation"""
        self.logger.info("Falling back to local mode...")
        return False  # Placeholder
    
    def _reload_model(self, error_info: ErrorInfo) -> bool:
        """Reload the current model"""
        self.logger.info("Reloading model...")
        return False  # Placeholder
    
    def _switch_to_fallback_model(self, error_info: ErrorInfo) -> bool:
        """Switch to a fallback model"""
        self.logger.info("Switching to fallback model...")
        return False  # Placeholder
    
    def _clear_model_cache(self, error_info: ErrorInfo) -> bool:
        """Clear model cache"""
        self.logger.info("Clearing model cache...")
        return False  # Placeholder
    
    def _sanitize_input(self, error_info: ErrorInfo) -> bool:
        """Sanitize input data"""
        self.logger.info("Sanitizing input...")
        return False  # Placeholder
    
    def _use_default_values(self, error_info: ErrorInfo) -> bool:
        """Use default values"""
        self.logger.info("Using default values...")
        return False  # Placeholder
    
    def _reset_ui_state(self, error_info: ErrorInfo) -> bool:
        """Reset UI state"""
        self.logger.info("Resetting UI state...")
        return False  # Placeholder
    
    def _reload_ui_components(self, error_info: ErrorInfo) -> bool:
        """Reload UI components"""
        self.logger.info("Reloading UI components...")
        return False  # Placeholder
    
    def register_recovery_strategy(self, category: ErrorCategory, strategy: Callable):
        """Register a custom recovery strategy"""
        if category not in self.recovery_strategies:
            self.recovery_strategies[category] = []
        self.recovery_strategies[category].append(strategy)
        self.logger.info(f"Registered recovery strategy {strategy.__name__} for {category.value}")
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """Get error statistics"""
        with self.lock:
            total_errors = len(self.errors)
            if total_errors == 0:
                return {"total_errors": 0}
            
            # Count by category
            category_counts = {}
            severity_counts = {}
            recovery_stats = {"attempted": 0, "successful": 0}
            
            for error in self.errors:
                # Category counts
                cat = error.category.value
                category_counts[cat] = category_counts.get(cat, 0) + 1
                
                # Severity counts
                sev = error.severity.value
                severity_counts[sev] = severity_counts.get(sev, 0) + 1
                
                # Recovery stats
                if error.recovery_attempted:
                    recovery_stats["attempted"] += 1
                    if error.recovery_successful:
                        recovery_stats["successful"] += 1
            
            # Recent errors (last hour)
            recent_threshold = datetime.now().timestamp() - 3600
            recent_errors = sum(1 for error in self.errors 
                              if error.timestamp.timestamp() > recent_threshold)
            
            return {
                "total_errors": total_errors,
                "recent_errors_1h": recent_errors,
                "category_breakdown": category_counts,
                "severity_breakdown": severity_counts,
                "recovery_stats": recovery_stats,
                "most_common_errors": dict(sorted(self.error_counts.items(), 
                                                key=lambda x: x[1], reverse=True)[:5])
            }
    
    def get_recent_errors(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent errors"""
        with self.lock:
            recent_errors = self.errors[-limit:] if self.errors else []
            return [
                {
                    "timestamp": error.timestamp.isoformat(),
                    "category": error.category.value,
                    "severity": error.severity.value,
                    "message": error.message,
                    "exception_type": error.exception_type,
                    "recovery_attempted": error.recovery_attempted,
                    "recovery_successful": error.recovery_successful,
                    "context": error.context
                }
                for error in reversed(recent_errors)
            ]
    
    def clear_errors(self):
        """Clear error history"""
        with self.lock:
            self.errors.clear()
            self.error_counts.clear()
        self.logger.info("Error history cleared")
    
    def export_error_report(self, file_path: str):
        """Export detailed error report"""
        try:
            report = {
                "generated_at": datetime.now().isoformat(),
                "statistics": self.get_error_statistics(),
                "recent_errors": self.get_recent_errors(50),
                "error_counts": self.error_counts
            }
            
            with open(file_path, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            self.logger.info(f"Error report exported to {file_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to export error report: {e}")


def error_handler_decorator(category: ErrorCategory = ErrorCategory.UNKNOWN,
                          severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                          attempt_recovery: bool = True,
                          reraise: bool = False):
    """Decorator for automatic error handling"""
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Get error handler instance (assuming it's available globally)
                error_handler = getattr(wrapper, '_error_handler', None)
                if error_handler:
                    context = {
                        "function": func.__name__,
                        "args": str(args)[:200],  # Limit context size
                        "kwargs": str(kwargs)[:200]
                    }
                    
                    recovery_successful = error_handler.handle_error(
                        e, category, severity, context, attempt_recovery
                    )
                    
                    if not recovery_successful and reraise:
                        raise
                    
                    return None if not recovery_successful else True
                else:
                    if reraise:
                        raise
                    return None
        
        return wrapper
    return decorator


# Global error handler instance
global_error_handler = EnhancedErrorHandler()


def set_global_error_handler(handler: EnhancedErrorHandler):
    """Set the global error handler instance"""
    global global_error_handler
    global_error_handler = handler


def handle_error(exception: Exception, category: ErrorCategory = ErrorCategory.UNKNOWN,
                severity: ErrorSeverity = ErrorSeverity.MEDIUM, context: Dict[str, Any] = None,
                attempt_recovery: bool = True) -> bool:
    """Convenience function for error handling"""
    return global_error_handler.handle_error(exception, category, severity, context, attempt_recovery)
