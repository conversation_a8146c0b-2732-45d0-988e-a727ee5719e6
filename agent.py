#!/usr/bin/env python3
"""
Self-Upgrading Ollama Agent with PyQt5 GUI
A Python agent that can interact with Ollama models and upgrade itself
"""

import sys
import os
import json
import requests
import subprocess
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional
import tempfile
import shutil

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
    QTextEdit, QLineEdit, QPushButton, QLabel, QComboBox,
    QWidget, QSplitter, QTabWidget, QProgressBar, QMessageBox,
    QFileDialog, QCheckBox, QSpinBox, QGroupBox, QScrollArea
)
from PyQt5.QtCore import QThread, pyqtSignal, QTimer, Qt
from PyQt5.QtGui import QFont, QTextCursor


class OllamaAPI:
    """Handle Ollama API interactions"""

    def __init__(self, base_url: str = "http://localhost:11434"):
        self.base_url = base_url

    def list_models(self) -> List[str]:
        """Get list of available models"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                data = response.json()
                return [model['name'] for model in data.get('models', [])]
            return []
        except Exception as e:
            print(f"Error listing models: {e}")
            return []

    def chat(self, model: str, messages: List[Dict], stream: bool = True):
        """Send chat request to Ollama"""
        try:
            payload = {
                "model": model,
                "messages": messages,
                "stream": stream
            }

            response = requests.post(
                f"{self.base_url}/api/chat",
                json=payload,
                stream=stream,
                timeout=30
            )

            if stream:
                return response
            else:
                return response.json()

        except Exception as e:
            print(f"Error in chat: {e}")
            return None

    def pull_model(self, model_name: str):
        """Pull/download a model"""
        try:
            payload = {"name": model_name}
            response = requests.post(
                f"{self.base_url}/api/pull",
                json=payload,
                stream=True,
                timeout=300  # 5 minutes for model downloads
            )
            return response
        except Exception as e:
            print(f"Error pulling model: {e}")
            return None


class ChatThread(QThread):
    """Thread for handling chat requests"""
    message_received = pyqtSignal(str)
    finished = pyqtSignal()

    def __init__(self, ollama_api, model, messages):
        super().__init__()
        self.ollama_api = ollama_api
        self.model = model
        self.messages = messages
        self.running = True

    def run(self):
        try:
            response = self.ollama_api.chat(self.model, self.messages, stream=True)
            if response and response.status_code == 200:
                for line in response.iter_lines():
                    if not self.running:
                        break
                    if line:
                        try:
                            data = json.loads(line.decode('utf-8'))
                            if 'message' in data and 'content' in data['message']:
                                content = data['message']['content']
                                self.message_received.emit(content)
                            # Check if this is the final message
                            if data.get('done', False):
                                break
                        except json.JSONDecodeError:
                            continue
                        except Exception as e:
                            print(f"Error processing line: {e}")
                            continue
            else:
                error_msg = "Failed to connect to Ollama. Please check if Ollama is running."
                if response:
                    error_msg += f" Status code: {response.status_code}"
                self.message_received.emit(error_msg)
        except Exception as e:
            self.message_received.emit(f"Error: {str(e)}")
        finally:
            self.finished.emit()

    def stop(self):
        self.running = False


class SelfUpgradeSystem:
    """Handle self-upgrade functionality"""

    def __init__(self, agent_instance):
        self.agent = agent_instance
        self.current_version = "1.0.0"
        self.upgrade_url = "https://raw.githubusercontent.com/example/agent/main/"

    def check_for_updates(self) -> Dict:
        """Check if updates are available"""
        try:
            # In a real implementation, this would check a remote server
            # For demo purposes, we'll simulate update availability
            return {
                "update_available": True,
                "latest_version": "1.1.0",
                "changelog": [
                    "Improved chat interface",
                    "Better error handling",
                    "New self-upgrade features"
                ]
            }
        except Exception as e:
            return {"error": str(e)}

    def analyze_current_code(self) -> str:
        """Analyze current code for improvement suggestions"""
        suggestions = []

        # Get current file content
        current_file = __file__
        try:
            with open(current_file, 'r') as f:
                content = f.read()

            # Simple analysis (in reality, this would be more sophisticated)
            if "TODO" in content:
                suggestions.append("Found TODO items that could be implemented")
            if "print(" in content:
                suggestions.append("Consider replacing print statements with proper logging")
            if len(content.split('\n')) > 500:
                suggestions.append("Code is getting large, consider modularization")

            return "\n".join(suggestions) if suggestions else "Code looks good!"

        except Exception as e:
            return f"Error analyzing code: {e}"

    def generate_upgrade_code(self, improvement_request: str) -> str:
        """Use Ollama to generate code improvements"""
        messages = [
            {
                "role": "system",
                "content": "You are a Python code improvement assistant. Generate improved code based on requests."
            },
            {
                "role": "user",
                "content": f"Improve this Python code: {improvement_request}"
            }
        ]

        try:
            response = self.agent.ollama_api.chat(
                self.agent.current_model,
                messages,
                stream=False
            )
            if response and 'message' in response:
                return response['message']['content']
        except Exception as e:
            return f"Error generating upgrade: {e}"

        return "No improvements generated"

    def apply_upgrade(self, new_code: str) -> bool:
        """Apply code upgrade (safely)"""
        try:
            # Create backup
            current_file = __file__
            backup_file = f"{current_file}.backup.{int(time.time())}"
            shutil.copy2(current_file, backup_file)

            # In a real implementation, you'd validate and apply the code
            # For safety, we'll just show what would be done
            return True

        except Exception as e:
            print(f"Error applying upgrade: {e}")
            return False


class OllamaAgentGUI(QMainWindow):
    """Main GUI for the Ollama Agent"""

    def __init__(self):
        super().__init__()
        self.ollama_api = OllamaAPI()
        self.upgrade_system = SelfUpgradeSystem(self)
        self.current_model = "llama2"  # Default model
        self.chat_history = []
        self.chat_thread = None

        self.init_ui()
        # Load models in a non-blocking way
        QTimer.singleShot(100, self.load_models)
        self.setup_auto_update_timer()

    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("Self-Upgrading Ollama Agent")
        self.setGeometry(100, 100, 1200, 800)

        # Create central widget and main layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # Create tab widget
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # Chat tab
        self.create_chat_tab()

        # Model management tab
        self.create_model_tab()

        # Self-upgrade tab
        self.create_upgrade_tab()

        # Settings tab
        self.create_settings_tab()

        # Status bar
        self.status_label = QLabel("Ready")
        main_layout.addWidget(self.status_label)

    def create_chat_tab(self):
        """Create the chat interface tab"""
        chat_widget = QWidget()
        layout = QVBoxLayout(chat_widget)

        # Model selection
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("Model:"))
        self.model_combo = QComboBox()
        self.model_combo.currentTextChanged.connect(self.on_model_changed)
        model_layout.addWidget(self.model_combo)
        model_layout.addStretch()
        layout.addLayout(model_layout)

        # Chat display
        self.chat_display = QTextEdit()
        self.chat_display.setReadOnly(True)
        self.chat_display.setFont(QFont("Consolas", 10))
        layout.addWidget(self.chat_display)

        # Input area
        input_layout = QHBoxLayout()
        self.chat_input = QLineEdit()
        self.chat_input.returnPressed.connect(self.send_message)
        self.send_button = QPushButton("Send")
        self.send_button.clicked.connect(self.send_message)
        self.clear_button = QPushButton("Clear")
        self.clear_button.clicked.connect(self.clear_chat)

        input_layout.addWidget(self.chat_input)
        input_layout.addWidget(self.send_button)
        input_layout.addWidget(self.clear_button)
        layout.addLayout(input_layout)

        self.tab_widget.addTab(chat_widget, "Chat")

    def create_model_tab(self):
        """Create the model management tab"""
        model_widget = QWidget()
        layout = QVBoxLayout(model_widget)

        # Available models
        layout.addWidget(QLabel("Available Models:"))
        self.model_list = QTextEdit()
        self.model_list.setMaximumHeight(200)
        layout.addWidget(self.model_list)

        # Model operations
        ops_layout = QHBoxLayout()
        self.refresh_models_btn = QPushButton("Refresh Models")
        self.refresh_models_btn.clicked.connect(self.load_models)
        ops_layout.addWidget(self.refresh_models_btn)

        self.pull_model_input = QLineEdit()
        self.pull_model_input.setPlaceholderText("Enter model name to pull (e.g., llama2)")
        ops_layout.addWidget(self.pull_model_input)

        self.pull_model_btn = QPushButton("Pull Model")
        self.pull_model_btn.clicked.connect(self.pull_model)
        ops_layout.addWidget(self.pull_model_btn)

        layout.addLayout(ops_layout)

        # Progress bar for model pulling
        self.model_progress = QProgressBar()
        self.model_progress.setVisible(False)
        layout.addWidget(self.model_progress)

        layout.addStretch()
        self.tab_widget.addTab(model_widget, "Models")

    def create_upgrade_tab(self):
        """Create the self-upgrade tab"""
        upgrade_widget = QWidget()
        layout = QVBoxLayout(upgrade_widget)

        # Current version info
        version_group = QGroupBox("Current Version")
        version_layout = QVBoxLayout(version_group)
        self.version_label = QLabel(f"Version: {self.upgrade_system.current_version}")
        version_layout.addWidget(self.version_label)
        layout.addWidget(version_group)

        # Update check
        update_group = QGroupBox("Updates")
        update_layout = QVBoxLayout(update_group)

        update_btn_layout = QHBoxLayout()
        self.check_updates_btn = QPushButton("Check for Updates")
        self.check_updates_btn.clicked.connect(self.check_updates)
        update_btn_layout.addWidget(self.check_updates_btn)

        self.auto_update_cb = QCheckBox("Auto-check for updates")
        self.auto_update_cb.setChecked(True)
        update_btn_layout.addWidget(self.auto_update_cb)
        update_btn_layout.addStretch()

        update_layout.addLayout(update_btn_layout)

        self.update_info = QTextEdit()
        self.update_info.setMaximumHeight(150)
        update_layout.addWidget(self.update_info)

        layout.addWidget(update_group)

        # Code analysis and improvement
        improve_group = QGroupBox("Self-Improvement")
        improve_layout = QVBoxLayout(improve_group)

        self.analyze_code_btn = QPushButton("Analyze Current Code")
        self.analyze_code_btn.clicked.connect(self.analyze_code)
        improve_layout.addWidget(self.analyze_code_btn)

        self.code_analysis = QTextEdit()
        self.code_analysis.setMaximumHeight(150)
        improve_layout.addWidget(self.code_analysis)

        self.improvement_input = QLineEdit()
        self.improvement_input.setPlaceholderText("Describe desired improvements...")
        improve_layout.addWidget(self.improvement_input)

        self.generate_improvement_btn = QPushButton("Generate Improvement Code")
        self.generate_improvement_btn.clicked.connect(self.generate_improvements)
        improve_layout.addWidget(self.generate_improvement_btn)

        self.improvement_code = QTextEdit()
        improve_layout.addWidget(self.improvement_code)

        layout.addWidget(improve_group)

        self.tab_widget.addTab(upgrade_widget, "Self-Upgrade")

    def create_settings_tab(self):
        """Create the settings tab"""
        settings_widget = QWidget()
        layout = QVBoxLayout(settings_widget)

        # Ollama settings
        ollama_group = QGroupBox("Ollama Settings")
        ollama_layout = QVBoxLayout(ollama_group)

        url_layout = QHBoxLayout()
        url_layout.addWidget(QLabel("Ollama URL:"))
        self.ollama_url_input = QLineEdit("http://localhost:11434")
        url_layout.addWidget(self.ollama_url_input)
        ollama_layout.addLayout(url_layout)

        self.test_connection_btn = QPushButton("Test Connection")
        self.test_connection_btn.clicked.connect(self.test_ollama_connection)
        ollama_layout.addWidget(self.test_connection_btn)

        layout.addWidget(ollama_group)

        # Chat settings
        chat_group = QGroupBox("Chat Settings")
        chat_layout = QVBoxLayout(chat_group)

        self.save_chat_cb = QCheckBox("Save chat history")
        self.save_chat_cb.setChecked(True)
        chat_layout.addWidget(self.save_chat_cb)

        self.export_chat_btn = QPushButton("Export Chat History")
        self.export_chat_btn.clicked.connect(self.export_chat)
        chat_layout.addWidget(self.export_chat_btn)

        layout.addWidget(chat_group)

        layout.addStretch()
        self.tab_widget.addTab(settings_widget, "Settings")

    def load_models(self):
        """Load available models from Ollama"""
        try:
            models = self.ollama_api.list_models()
            self.model_combo.clear()

            if models:
                self.model_combo.addItems(models)
                model_text = "\n".join(models)
                if self.current_model not in models:
                    self.current_model = models[0]
                    self.model_combo.setCurrentText(self.current_model)
                self.status_label.setText(f"Loaded {len(models)} models")
            else:
                model_text = "No models available. Please check Ollama connection."
                self.status_label.setText("No models found")

            self.model_list.setText(model_text)

        except Exception as e:
            error_msg = f"Error loading models: {str(e)}"
            self.model_list.setText(error_msg)
            self.status_label.setText("Failed to load models")

    def on_model_changed(self, model_name):
        """Handle model selection change"""
        self.current_model = model_name
        self.status_label.setText(f"Selected model: {model_name}")

    def send_message(self):
        """Send a chat message"""
        message = self.chat_input.text().strip()
        if not message:
            return

        # Add user message to chat
        self.add_to_chat("You", message)
        self.chat_input.clear()

        # Add to history
        self.chat_history.append({"role": "user", "content": message})

        # Send to Ollama
        self.send_button.setEnabled(False)
        self.status_label.setText("Generating response...")

        self.chat_thread = ChatThread(
            self.ollama_api,
            self.current_model,
            self.chat_history.copy()
        )
        self.chat_thread.message_received.connect(self.on_message_received)
        self.chat_thread.finished.connect(self.on_chat_finished)
        self.chat_thread.start()

    def on_message_received(self, content):
        """Handle received message content"""
        cursor = self.chat_display.textCursor()
        cursor.movePosition(QTextCursor.End)
        cursor.insertText(content)
        self.chat_display.setTextCursor(cursor)
        self.chat_display.ensureCursorVisible()

    def on_chat_finished(self):
        """Handle chat completion"""
        # Get the complete response
        cursor = self.chat_display.textCursor()
        cursor.movePosition(QTextCursor.End)
        cursor.insertText("\n\n")

        self.send_button.setEnabled(True)
        self.status_label.setText("Ready")

        # Add assistant response to history (simplified)
        # In a real implementation, you'd capture the complete response

    def add_to_chat(self, sender, message):
        """Add a message to the chat display"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {sender}: {message}\n\n"
        self.chat_display.append(formatted_message)

    def clear_chat(self):
        """Clear the chat display and history"""
        self.chat_display.clear()
        self.chat_history.clear()
        self.status_label.setText("Chat cleared")

    def pull_model(self):
        """Pull a new model"""
        model_name = self.pull_model_input.text().strip()
        if not model_name:
            QMessageBox.warning(self, "Warning", "Please enter a model name")
            return

        self.model_progress.setVisible(True)
        self.model_progress.setRange(0, 0)  # Indeterminate progress
        self.pull_model_btn.setEnabled(False)
        self.status_label.setText(f"Pulling model: {model_name}")

        # In a real implementation, you'd handle this in a separate thread
        QTimer.singleShot(3000, self.on_model_pull_finished)  # Simulate completion

    def on_model_pull_finished(self):
        """Handle model pull completion"""
        self.model_progress.setVisible(False)
        self.pull_model_btn.setEnabled(True)
        self.status_label.setText("Model pull completed")
        self.load_models()  # Refresh model list

    def check_updates(self):
        """Check for available updates"""
        self.status_label.setText("Checking for updates...")
        update_info = self.upgrade_system.check_for_updates()

        if "error" in update_info:
            self.update_info.setText(f"Error checking updates: {update_info['error']}")
        elif update_info.get("update_available"):
            info_text = f"Update available: v{update_info['latest_version']}\n\n"
            info_text += "Changelog:\n" + "\n".join(f"• {item}" for item in update_info['changelog'])
            self.update_info.setText(info_text)
        else:
            self.update_info.setText("No updates available")

        self.status_label.setText("Update check completed")

    def analyze_code(self):
        """Analyze current code for improvements"""
        self.status_label.setText("Analyzing code...")
        analysis = self.upgrade_system.analyze_current_code()
        self.code_analysis.setText(analysis)
        self.status_label.setText("Code analysis completed")

    def generate_improvements(self):
        """Generate code improvements using Ollama"""
        improvement_request = self.improvement_input.text().strip()
        if not improvement_request:
            QMessageBox.warning(self, "Warning", "Please describe desired improvements")
            return

        self.status_label.setText("Generating improvements...")
        self.generate_improvement_btn.setEnabled(False)

        # Use QTimer to run this asynchronously to prevent UI freezing
        def run_improvement():
            try:
                improved_code = self.upgrade_system.generate_upgrade_code(improvement_request)
                self.improvement_code.setText(improved_code)
            except Exception as e:
                self.improvement_code.setText(f"Error generating improvements: {str(e)}")
            finally:
                self.generate_improvement_btn.setEnabled(True)
                self.status_label.setText("Improvements generated")

        QTimer.singleShot(100, run_improvement)

    def test_ollama_connection(self):
        """Test connection to Ollama"""
        url = self.ollama_url_input.text().strip()
        self.ollama_api.base_url = url

        try:
            models = self.ollama_api.list_models()
            if models:
                QMessageBox.information(self, "Success", f"Connected successfully!\nFound {len(models)} models")
                self.load_models()
            else:
                QMessageBox.warning(self, "Warning", "Connected but no models found")
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Connection failed: {str(e)}")

    def export_chat(self):
        """Export chat history to file"""
        if not self.chat_history:
            QMessageBox.information(self, "Info", "No chat history to export")
            return

        filename, _ = QFileDialog.getSaveFileName(
            self, "Export Chat History", "chat_history.json", "JSON Files (*.json)"
        )

        if filename:
            try:
                with open(filename, 'w') as f:
                    json.dump(self.chat_history, f, indent=2)
                QMessageBox.information(self, "Success", "Chat history exported successfully")
            except Exception as e:
                QMessageBox.critical(self, "Error", f"Export failed: {str(e)}")

    def setup_auto_update_timer(self):
        """Setup timer for automatic update checks"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.auto_check_updates)
        self.update_timer.start(3600000)  # Check every hour

    def auto_check_updates(self):
        """Automatically check for updates if enabled"""
        if hasattr(self, 'auto_update_cb') and self.auto_update_cb.isChecked():
            self.check_updates()

    def closeEvent(self, event):
        """Handle application close"""
        if self.chat_thread and self.chat_thread.isRunning():
            self.chat_thread.stop()
            self.chat_thread.wait()
        event.accept()


def main():
    """Main application entry point"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # Modern look

    # Check if Ollama is running
    try:
        requests.get("http://localhost:11434/api/tags", timeout=5)
    except:
        QMessageBox.warning(
            None,
            "Ollama Not Found",
            "Ollama doesn't appear to be running.\n"
            "Please start Ollama before using this application.\n\n"
            "Install: https://ollama.ai/\n"
            "Start: 'ollama serve'"
        )

    window = OllamaAgentGUI()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()