#!/usr/bin/env python3
"""
Test script for the enhanced self-upgrading functionality
Tests code validation, testing, and upgrade application
"""

import sys
import os
import tempfile
import shutil
from unittest.mock import Mock, patch

# Import the agent classes
from agent import SelfUpgradeSystem, OllamaAgentGUI

def test_code_validation():
    """Test the code validation functionality"""
    print("Testing Code Validation...")
    
    # Create a mock agent
    mock_agent = Mock()
    upgrade_system = SelfUpgradeSystem(mock_agent)
    
    # Test valid Python code
    valid_code = """
import sys
import os
import json

def test_function():
    return "Hello, World!"

class TestClass:
    def __init__(self):
        self.value = 42
    
    def get_value(self):
        return self.value
"""
    
    validation_result = upgrade_system.validate_python_code(valid_code)
    print(f"Valid code test: {validation_result}")
    assert validation_result['syntax_valid'], "Valid code should pass syntax check"
    
    # Test invalid Python code
    invalid_code = """
import sys
def broken_function(
    return "This is broken"
"""
    
    validation_result = upgrade_system.validate_python_code(invalid_code)
    print(f"Invalid code test: {validation_result}")
    assert not validation_result['syntax_valid'], "Invalid code should fail syntax check"
    
    # Test code with dangerous operations
    dangerous_code = """
import os
os.system("rm -rf /")
"""
    
    validation_result = upgrade_system.validate_python_code(dangerous_code)
    print(f"Dangerous code test: {validation_result}")
    assert not validation_result['valid'], "Dangerous code should be flagged"
    assert len(validation_result['issues']) > 0, "Should detect dangerous operations"
    
    print("✓ Code validation tests passed")

def test_test_environment():
    """Test the test environment creation and cleanup"""
    print("\nTesting Test Environment...")
    
    mock_agent = Mock()
    upgrade_system = SelfUpgradeSystem(mock_agent)
    
    # Test environment creation
    test_dir = upgrade_system.create_test_environment()
    assert test_dir is not None, "Should create test directory"
    assert os.path.exists(test_dir), "Test directory should exist"
    
    test_file = os.path.join(test_dir, "test_agent.py")
    assert os.path.exists(test_file), "Test file should be copied"
    
    # Clean up
    shutil.rmtree(test_dir)
    assert not os.path.exists(test_dir), "Test directory should be cleaned up"
    
    print("✓ Test environment tests passed")

def test_code_testing():
    """Test the code testing functionality"""
    print("\nTesting Code Testing...")
    
    mock_agent = Mock()
    upgrade_system = SelfUpgradeSystem(mock_agent)
    
    # Create test environment
    test_dir = upgrade_system.create_test_environment()
    
    try:
        # Test with current code (should pass)
        test_results = upgrade_system.run_tests(test_dir)
        print(f"Current code test: {test_results}")
        assert test_results['success'], "Current code should pass tests"
        assert test_results['tests_passed'] > 0, "Should have passing tests"
        
        # Test with broken code
        broken_code = """
import sys
# This will cause import errors
from non_existent_module import something
"""
        
        test_results = upgrade_system.run_tests(test_dir, broken_code)
        print(f"Broken code test: {test_results}")
        assert not test_results['success'], "Broken code should fail tests"
        
    finally:
        # Clean up
        shutil.rmtree(test_dir)
    
    print("✓ Code testing tests passed")

def test_self_improvement_generation():
    """Test the self-improvement code generation"""
    print("\nTesting Self-Improvement Generation...")
    
    # Create a mock agent with mock API
    mock_agent = Mock()
    mock_api = Mock()
    mock_agent.ollama_api = mock_api
    mock_agent.current_model = "test_model"
    
    # Mock the chat response
    mock_response = {
        'message': {
            'content': '''#!/usr/bin/env python3
"""
Improved Self-Upgrading Ollama Agent
"""
import sys
import os

def improved_function():
    """This is an improved function"""
    return "Improved functionality"

class ImprovedClass:
    def __init__(self):
        self.version = "2.0.0"
'''
        }
    }
    mock_api.chat.return_value = mock_response
    
    upgrade_system = SelfUpgradeSystem(mock_agent)
    
    # Test self-improvement generation
    improvement_goal = "Add better error handling and logging"
    result = upgrade_system.generate_self_improvement(improvement_goal)
    
    print(f"Generated improvement (first 200 chars): {result[:200]}...")
    assert isinstance(result, str), "Should return string"
    assert len(result) > 0, "Should generate some code"
    assert "def" in result or "class" in result, "Should contain Python code structures"
    
    print("✓ Self-improvement generation tests passed")

def test_upgrade_workflow():
    """Test the complete upgrade workflow"""
    print("\nTesting Complete Upgrade Workflow...")
    
    mock_agent = Mock()
    upgrade_system = SelfUpgradeSystem(mock_agent)
    
    # Create a simple valid improvement
    simple_improvement = '''#!/usr/bin/env python3
"""
Simple test improvement
"""
import sys
import os
import json
import requests
from PyQt5.QtWidgets import QApplication

class OllamaAPI:
    def __init__(self):
        self.base_url = "http://localhost:11434"
    
    def list_models(self):
        return []
    
    def chat(self, model, messages, stream=True):
        return None

class SelfUpgradeSystem:
    def __init__(self, agent):
        self.agent = agent

def main():
    pass

if __name__ == "__main__":
    main()
'''
    
    # Test the complete workflow (but don't actually apply)
    print("1. Validating code...")
    validation = upgrade_system.validate_python_code(simple_improvement)
    print(f"   Validation result: {validation['valid']}")
    
    print("2. Creating test environment...")
    test_dir = upgrade_system.create_test_environment()
    assert test_dir is not None
    
    try:
        print("3. Running tests...")
        test_results = upgrade_system.run_tests(test_dir, simple_improvement)
        print(f"   Test result: {test_results['success']}")
        
        # Note: We won't actually apply the upgrade in tests
        print("4. Upgrade application (skipped in test)")
        
    finally:
        shutil.rmtree(test_dir)
    
    print("✓ Complete upgrade workflow tests passed")

def main():
    """Run all self-upgrade tests"""
    print("Testing Enhanced Self-Upgrading Functionality")
    print("=" * 60)
    
    try:
        test_code_validation()
        test_test_environment()
        test_code_testing()
        test_self_improvement_generation()
        test_upgrade_workflow()
        
        print("\n" + "=" * 60)
        print("✅ All self-upgrade tests passed!")
        print("\nNew Self-Upgrading Features:")
        print("• Code syntax and safety validation")
        print("• Isolated test environment creation")
        print("• Automated testing of generated code")
        print("• Safe code upgrade application with backups")
        print("• AI-powered self-improvement generation")
        print("• Complete validation → testing → upgrade workflow")
        
        print("\nHow to use the self-upgrading features:")
        print("1. Run: python3 agent.py")
        print("2. Go to 'Self-Upgrade' tab")
        print("3. Enter improvement description")
        print("4. Click 'Generate Self-Improvement'")
        print("5. Click 'Validate Code' to check syntax and safety")
        print("6. Click 'Test Code' to run automated tests")
        print("7. Click 'Apply Upgrade' to implement changes")
        print("8. Restart the application to use upgraded code")
        
    except Exception as e:
        print(f"\n❌ Tests failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
