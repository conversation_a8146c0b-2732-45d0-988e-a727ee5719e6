#!/usr/bin/env python3
"""
Enhanced Self-Upgrade System with improved security and reliability
"""

import hashlib
import json
import os
import shutil
import tempfile
import time
import subprocess
import sys
from datetime import datetime
from typing import Dict, List, Optional
import requests
import semantic_version


class EnhancedSelfUpgradeSystem:
    """Enhanced self-upgrade system with proper version management and security"""
    
    def __init__(self, agent_instance, config_file: str = "upgrade_config.json"):
        self.agent = agent_instance
        self.config_file = config_file
        self.config = self._load_config()
        self.current_version = semantic_version.Version(self.config.get("version", "1.0.0"))
        
    def _load_config(self) -> Dict:
        """Load upgrade configuration from file"""
        default_config = {
            "version": "1.0.0",
            "upgrade_url": "https://api.github.com/repos/user/repo/releases/latest",
            "public_key": None,  # For signature verification
            "auto_backup": True,
            "max_backups": 5,
            "rollback_timeout": 300  # 5 minutes
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    return {**default_config, **config}
            except Exception as e:
                print(f"Error loading config: {e}")
        
        return default_config
    
    def _save_config(self):
        """Save current configuration to file"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def check_for_updates(self) -> Dict:
        """Check for updates with proper version comparison"""
        try:
            response = requests.get(self.config["upgrade_url"], timeout=10)
            if response.status_code == 200:
                release_data = response.json()
                latest_version = semantic_version.Version(release_data["tag_name"].lstrip('v'))
                
                return {
                    "update_available": latest_version > self.current_version,
                    "latest_version": str(latest_version),
                    "current_version": str(self.current_version),
                    "download_url": release_data.get("zipball_url"),
                    "changelog": release_data.get("body", ""),
                    "published_at": release_data.get("published_at")
                }
            else:
                return {"error": f"Failed to check updates: HTTP {response.status_code}"}
        except Exception as e:
            return {"error": f"Update check failed: {str(e)}"}
    
    def verify_code_integrity(self, code: str, expected_hash: str = None) -> Dict:
        """Verify code integrity using SHA-256 hash"""
        try:
            code_hash = hashlib.sha256(code.encode('utf-8')).hexdigest()
            
            if expected_hash:
                integrity_valid = code_hash == expected_hash
            else:
                integrity_valid = True  # No hash to compare against
            
            return {
                "valid": integrity_valid,
                "hash": code_hash,
                "expected_hash": expected_hash
            }
        except Exception as e:
            return {
                "valid": False,
                "error": f"Integrity check failed: {str(e)}"
            }
    
    def enhanced_code_validation(self, code: str) -> Dict:
        """Enhanced code validation with security and compatibility checks"""
        try:
            # Basic syntax validation
            ast.parse(code)
            
            issues = []
            warnings = []
            
            # Enhanced dangerous operation detection
            dangerous_patterns = {
                'system_calls': [r'os\.system\(', r'subprocess\.call\(', r'subprocess\.run\('],
                'code_execution': [r'exec\(', r'eval\(', r'compile\('],
                'file_operations': [r'open\(.+[\'"]w[\'"]', r'shutil\.rmtree\(', r'os\.remove\('],
                'network_operations': [r'urllib\.request', r'socket\.socket\('],
                'import_manipulation': [r'__import__\(', r'importlib\.import_module\(']
            }
            
            for category, patterns in dangerous_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, code):
                        issues.append(f"Potentially dangerous {category}: {pattern}")
            
            # Check for required imports and dependencies
            required_imports = ['sys', 'os', 'json', 'requests', 'PyQt5']
            missing_imports = []
            for imp in required_imports:
                if imp not in code:
                    missing_imports.append(imp)
            
            # Check for breaking changes
            breaking_changes = self._detect_breaking_changes(code)
            if breaking_changes:
                warnings.extend(breaking_changes)
            
            return {
                "valid": len(issues) == 0,
                "issues": issues,
                "warnings": warnings,
                "missing_imports": missing_imports,
                "syntax_valid": True
            }
            
        except SyntaxError as e:
            return {
                "valid": False,
                "issues": [f"Syntax error: {str(e)}"],
                "warnings": [],
                "missing_imports": [],
                "syntax_valid": False
            }
    
    def _detect_breaking_changes(self, new_code: str) -> List[str]:
        """Detect potential breaking changes in the new code"""
        warnings = []
        
        # Check for removed classes or methods
        current_file = __file__
        try:
            with open(current_file, 'r') as f:
                current_code = f.read()
            
            # Simple check for class and function definitions
            import re
            current_classes = set(re.findall(r'class\s+(\w+)', current_code))
            new_classes = set(re.findall(r'class\s+(\w+)', new_code))
            
            removed_classes = current_classes - new_classes
            if removed_classes:
                warnings.append(f"Removed classes: {', '.join(removed_classes)}")
            
            current_functions = set(re.findall(r'def\s+(\w+)', current_code))
            new_functions = set(re.findall(r'def\s+(\w+)', new_code))
            
            removed_functions = current_functions - new_functions
            if removed_functions:
                warnings.append(f"Removed functions: {', '.join(removed_functions)}")
                
        except Exception as e:
            warnings.append(f"Could not analyze breaking changes: {str(e)}")
        
        return warnings
    
    def create_backup(self) -> str:
        """Create a versioned backup of the current code"""
        try:
            current_file = __file__
            timestamp = int(time.time())
            backup_dir = "backups"
            
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
            
            backup_file = os.path.join(backup_dir, f"agent_v{self.current_version}_{timestamp}.py")
            shutil.copy2(current_file, backup_file)
            
            # Create backup metadata
            metadata = {
                "version": str(self.current_version),
                "timestamp": timestamp,
                "datetime": datetime.now().isoformat(),
                "original_file": current_file,
                "backup_file": backup_file
            }
            
            metadata_file = backup_file.replace('.py', '_metadata.json')
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            # Clean up old backups
            self._cleanup_old_backups()
            
            return backup_file
            
        except Exception as e:
            raise Exception(f"Backup creation failed: {str(e)}")
    
    def _cleanup_old_backups(self):
        """Remove old backups beyond the configured limit"""
        try:
            backup_dir = "backups"
            if not os.path.exists(backup_dir):
                return
            
            backup_files = [f for f in os.listdir(backup_dir) if f.endswith('.py')]
            backup_files.sort(key=lambda x: os.path.getctime(os.path.join(backup_dir, x)))
            
            max_backups = self.config.get("max_backups", 5)
            if len(backup_files) > max_backups:
                files_to_remove = backup_files[:-max_backups]
                for file in files_to_remove:
                    file_path = os.path.join(backup_dir, file)
                    metadata_path = file_path.replace('.py', '_metadata.json')
                    
                    if os.path.exists(file_path):
                        os.remove(file_path)
                    if os.path.exists(metadata_path):
                        os.remove(metadata_path)
                        
        except Exception as e:
            print(f"Backup cleanup failed: {str(e)}")
    
    def rollback_to_backup(self, backup_file: str) -> Dict:
        """Rollback to a specific backup"""
        try:
            if not os.path.exists(backup_file):
                return {"success": False, "error": "Backup file not found"}
            
            current_file = __file__
            
            # Create a backup of current state before rollback
            emergency_backup = f"{current_file}.emergency_backup.{int(time.time())}"
            shutil.copy2(current_file, emergency_backup)
            
            # Restore from backup
            shutil.copy2(backup_file, current_file)
            
            return {
                "success": True,
                "backup_file": backup_file,
                "emergency_backup": emergency_backup
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Rollback failed: {str(e)}"
            }
