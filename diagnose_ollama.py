#!/usr/bin/env python3
"""
Ollama Connection Diagnostic Tool
Helps troubleshoot connection and timeout issues
"""

import requests
import time
import subprocess
import sys

def check_ollama_service():
    """Check if Ollama service is running"""
    print("🔍 Checking Ollama service status...")
    
    try:
        # Check if ollama process is running
        result = subprocess.run(['pgrep', '-f', 'ollama'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Ollama process is running")
            return True
        else:
            print("❌ Ollama process not found")
            return False
    except Exception as e:
        print(f"⚠️  Could not check process status: {e}")
        return False

def test_ollama_connection():
    """Test basic connection to Ollama"""
    print("\n🔗 Testing Ollama connection...")
    
    base_url = "http://localhost:11434"
    
    try:
        # Test basic connectivity
        print("  Testing basic connectivity...")
        response = requests.get(f"{base_url}/api/tags", timeout=10)
        
        if response.status_code == 200:
            print("✅ Connection successful!")
            
            # Parse models
            data = response.json()
            models = data.get('models', [])
            
            if models:
                print(f"✅ Found {len(models)} models:")
                for model in models[:5]:  # Show first 5 models
                    name = model.get('name', 'Unknown')
                    size = model.get('size', 0)
                    size_gb = size / (1024**3) if size > 0 else 0
                    print(f"   • {name} ({size_gb:.1f} GB)")
                
                if len(models) > 5:
                    print(f"   ... and {len(models) - 5} more")
                
                return True, models
            else:
                print("⚠️  Connected but no models found")
                return True, []
        else:
            print(f"❌ Connection failed with status code: {response.status_code}")
            return False, []
            
    except requests.exceptions.Timeout:
        print("❌ Connection timed out (Ollama might be starting up)")
        return False, []
    except requests.exceptions.ConnectionError:
        print("❌ Connection refused (Ollama not running or wrong port)")
        return False, []
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False, []

def test_model_performance(models):
    """Test performance of available models"""
    if not models:
        print("\n⚠️  No models available for performance testing")
        return
    
    print("\n⚡ Testing model performance...")
    
    # Find a small model for testing
    test_model = None
    for model in models:
        name = model.get('name', '').lower()
        if any(size in name for size in ['1b', '0.5b', '0.6b', 'tiny', 'micro']):
            test_model = model.get('name')
            break
    
    if not test_model and models:
        test_model = models[0].get('name')
    
    if not test_model:
        print("❌ No suitable model found for testing")
        return
    
    print(f"  Testing with model: {test_model}")
    
    try:
        start_time = time.time()
        
        payload = {
            "model": test_model,
            "messages": [
                {"role": "user", "content": "Say 'Hello' in one word."}
            ],
            "stream": False
        }
        
        response = requests.post(
            "http://localhost:11434/api/chat",
            json=payload,
            timeout=60
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if response.status_code == 200:
            print(f"✅ Model responded in {duration:.1f} seconds")
            
            if duration < 5:
                print("   🚀 Very fast response!")
            elif duration < 15:
                print("   ⚡ Good response time")
            elif duration < 30:
                print("   🔄 Moderate response time")
            else:
                print("   🐌 Slow response time - consider using a smaller model")
                
        else:
            print(f"❌ Model test failed with status: {response.status_code}")
            
    except requests.exceptions.Timeout:
        print("❌ Model test timed out - model is too slow or busy")
    except Exception as e:
        print(f"❌ Model test error: {e}")

def provide_recommendations():
    """Provide recommendations based on test results"""
    print("\n💡 Recommendations:")
    print("   1. If Ollama is not running:")
    print("      • Start with: ollama serve")
    print("      • Or run in background: nohup ollama serve &")
    print("")
    print("   2. If models are too slow:")
    print("      • Install faster models:")
    print("        - ollama pull llama3.2:1b (very fast)")
    print("        - ollama pull qwen:0.5b (ultra fast)")
    print("        - ollama pull tinyllama (tiny but functional)")
    print("")
    print("   3. If timeouts persist:")
    print("      • Increase timeout in agent settings")
    print("      • Use smaller models for better responsiveness")
    print("      • Check system resources (CPU/RAM usage)")
    print("")
    print("   4. For self-upgrading features:")
    print("      • Use coding-focused models:")
    print("        - ollama pull qwen2.5-coder:1.5b (fast coder)")
    print("        - ollama pull deepseek-coder:6.7b (better but slower)")

def main():
    """Run complete Ollama diagnostic"""
    print("Ollama Connection Diagnostic Tool")
    print("=" * 40)
    
    # Check if Ollama service is running
    service_running = check_ollama_service()
    
    # Test connection
    connected, models = test_ollama_connection()
    
    # Test model performance if connected
    if connected and models:
        test_model_performance(models)
    
    # Provide recommendations
    provide_recommendations()
    
    print("\n" + "=" * 40)
    if connected:
        print("✅ Ollama is working! You can now use the agent.")
    else:
        print("❌ Ollama connection issues detected.")
        if not service_running:
            print("   Try starting Ollama with: ollama serve")
        else:
            print("   Ollama is running but not responding properly.")

if __name__ == "__main__":
    main()
