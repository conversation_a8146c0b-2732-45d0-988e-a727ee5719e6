class NexusLight:
    """
    Handles inter-agent communication via a private chat interface.
    Simulates sending and receiving messages between agents.
    """
    def __init__(self, agent_name: str, orchestrator_ref=None, parent_agent_name: str = None):
        """
        Initializes the NexusLight instance for a specific agent.

        Args:
            agent_name (str): The name of the agent using this NexusLight instance.
            orchestrator_ref: A reference to the Orchestrator or parent agent for
                               communication. This could be an object instance or a callback.
            parent_agent_name (str, optional): The name of the parent agent if this is a sub-agent. Defaults to None.
        """
        self.agent_name = agent_name
        self.orchestrator_ref = orchestrator_ref
        self.parent_agent_name = parent_agent_name # Store parent agent name for hierarchy
        self.nexus_mind_light = None  # Placeholder for Nexus Mind Light agent

    def send_message(self, recipient_agent_name: str, message_payload: dict, sender_hierarchy: list = None, recipient_hierarchy: list = None):
        """
        Simulates sending a message to another agent.

        Args:
            recipient_agent_name (str): The name of the agent to send the message to.
            message_payload (dict): The content of the message, including type and content.
            sender_hierarchy (list, optional): List of agent names representing the sender's hierarchy (e.g., ["Orchestrator", "MainAgent", "SubAgent"]). Defaults to None.
            recipient_hierarchy (list, optional): List of agent names representing the recipient's hierarchy. Defaults to None.
        """
        message_type = message_payload.get("type", "default")
        message_content = message_payload.get("content", "")
        
        # Add hierarchy information to the message payload
        message_payload["sender_name"] = self.agent_name
        message_payload["sender_parent"] = self.parent_agent_name
        message_payload["recipient_name"] = recipient_agent_name
        message_payload["sender_hierarchy"] = sender_hierarchy if sender_hierarchy is not None else [self.agent_name]
        message_payload["recipient_hierarchy"] = recipient_hierarchy if recipient_hierarchy is not None else [recipient_agent_name]

        print(f"NexusLight ({self.agent_name}) sending message to {recipient_agent_name} (Type: {message_type}): {message_content}")
        if self.orchestrator_ref:
            # The orchestrator's handle_message will now receive the full payload with hierarchy
            self.orchestrator_ref.handle_message(self.agent_name, message_payload)

    def receive_message(self, sender_agent_name: str, message_payload: dict):
        """
        Simulates receiving a message from another agent.
        Forwards the message to the orchestrator for GUI display.

        Args:
            sender_agent_name (str): The name of the agent that sent the message.
            message_payload (dict): The content of the received message, including hierarchy.
        """
        message_content = message_payload.get("content", "")
        print(f"NexusLight ({self.agent_name}) received message from {sender_agent_name}: {message_content}")
        if self.orchestrator_ref:
            # The orchestrator's handle_message will now receive the full payload with hierarchy
            self.orchestrator_ref.handle_message(sender_agent_name, message_payload)
        # Further processing logic would go here, e.g., parsing commands, updating state.

    def query_orchestrator_for_directives(self):
        """
        Allows agents to query the Orchestrator for available tasks.
        """
        print(f"NexusLight ({self.agent_name}): Querying Orchestrator for available tasks...")
        if self.orchestrator_ref:
            # This will now directly call the get_available_task method on the Orchestrator
            task = self.orchestrator_ref.get_available_task(self.agent_name)
            if task:
                return {"type": "task_assigned", "task": task}
            else:
                return {"type": "no_task_available", "content": "No tasks currently available."}
        return {"type": "error", "content": "Orchestrator reference not set."}

# Example Usage (for testing purposes, can be removed later)
if __name__ == "__main__":
    # Mock Orchestrator for demonstration
    class MockOrchestrator:
        def receive_message(self, sender, msg):
            print(f"MockOrchestrator received message from {sender}: {msg}")
        def get_directives(self, agent_name):
            print(f"MockOrchestrator providing directives for {agent_name}")
            return f"Directive for {agent_name}: Perform task X."

    mock_orchestrator = MockOrchestrator()

    agent1_nexus = NexusLight(agent_name="AgentAlpha", orchestrator_ref=mock_orchestrator)
    agent2_nexus = NexusLight(agent_name="AgentBeta")

    agent1_nexus.send_message("AgentBeta", "Hello AgentBeta, how are you?")
    agent2_nexus.receive_message("AgentAlpha", "Hello AgentBeta, how are you?")

    agent2_nexus.send_message("AgentAlpha", "I am fine, AgentAlpha. What about you?")
    agent1_nexus.receive_message("AgentBeta", "I am fine, AgentAlpha. What about you?")

    agent1_nexus.send_message("Orchestrator", "Reporting task completion.")
    print(agent1_nexus.query_orchestrator_for_directives())