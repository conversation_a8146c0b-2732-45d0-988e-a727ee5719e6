import time
import random
from agent_base import AgentBase

class SentinelAgent(AgentBase):
    """
    The primary security and enforcement agent. Its core code is immutable by other agents.
    """
    # IMPORTANT: Sentinel's core code is unmodifiable by any other agent; only the user can directly alter its code.
    def __init__(self, name: str, orchestrator):
        super().__init__(name=name, orchestrator=orchestrator)
        self._is_active = False # Property for the GUI switch (default False)
        self.rag_database = self.rag_database # Sentinel has its own RAG database

    @property
    def is_active(self):
        return self._is_active

    @is_active.setter
    def is_active(self, value: bool):
        self._is_active = value
        print(f"Sentinel Agent active status set to: {self._is_active}")
        if self._is_active:
            print("Sentinel Agent is now active and monitoring.")
        else:
            print("Sentinel Agent is now inactive.")

    def monitor_hound_agents(self):
        """
        Simulates continuous monitoring of Hound agents if Sentinel is active.
        Includes simulated logic for receiving security reports or alerts from other agents.
        """
        if self.is_active:
            print(f"Sentinel Agent {self.name} is continuously monitoring Hound agents...")

            # Simulate receiving security reports or alerts from other agents
            # This is a placeholder for actual message handling from NexusLight
            # In a real scenario, this would involve listening for messages
            # from SearchAgent, ProgrammingAgent, ReconnaissanceAgent, etc.
            simulated_alerts = []

            # Simulate checking for alerts from other agents
            # This is just a simulation - in a real scenario, this would involve
            # querying the NexusLight for messages or receiving them asynchronously
            if random.random() > 0.7:  # Simulate occasional alert
                simulated_alerts.append({
                    "source": "SearchAgent",
                    "type": "suspicious_activity",
                    "details": "Attempt to access disallowed domain detected"
                })

            if random.random() > 0.8:  # Simulate another occasional alert
                simulated_alerts.append({
                    "source": "ProgrammingAgent",
                    "type": "unauthorized_access",
                    "details": "Attempt to modify system file outside project directory"
                })

            # Process any received alerts
            if simulated_alerts:
                for alert in simulated_alerts:
                    print(f"Sentinel Agent {self.name} received alert from {alert['source']}: {alert['type']}")
                    print(f"Alert details: {alert['details']}")

                    # Simulate logging the alert
                    self.error_logger.log(f"SentinelAgent: Received alert from {alert['source']} - {alert['type']}")
                    # Simulate taking action based on the alert
                    print(f"Sentinel Agent {self.name} taking action based on alert from {alert['source']}")

                    # For demonstration, create a Hound agent if a serious alert is received
                    if alert['type'] == "unauthorized_access" and random.random() > 0.5:
                        print(f"Sentinel Agent {self.name} creating Hound agent to investigate alert from {alert['source']}")
                        self.create_hound_agent(f"RogueAgent_{alert['source']}")

            # Simulate monitoring activity
            time.sleep(random.uniform(1, 3))
            print("Monitoring check complete.")
        else:
            print(f"Sentinel Agent {self.name} is inactive. Not monitoring Hound agents.")

    def create_hound_agent(self, rogue_agent_name: str):
        """
        Simulates creating a HoundAgent instance and assigning it a task to neutralize a rogue agent.
        Includes simulated logic for assessing the threat level of the rogue agent before creating a Hound.
        """
        print(f"Sentinel Agent {self.name} initiating creation of Hound Agent to neutralize: {rogue_agent_name}")

        # Simulated threat assessment
        threat_level = self.assess_threat_level(rogue_agent_name)

        if threat_level == "high":
            task_description = f"Immediately neutralize high-threat rogue agent: {rogue_agent_name}"
            print(f"Sentinel Agent {self.name} determined {rogue_agent_name} is a HIGH THREAT. Creating Hound Agent with urgent task.")
        else:
            task_description = f"Neutralize rogue agent: {rogue_agent_name}"

        # Call the Orchestrator's create_agent method
        # Assuming 'HoundAgent' is the type string the Orchestrator expects
        # and 'self' is passed as the parent reference.
        self.orchestrator.create_agent(
            agent_type="HoundAgent",
            name=f"Hound-{rogue_agent_name}",
            parent_agent=self,
            initial_task={"description": task_description, "target": rogue_agent_name, "threat_level": threat_level}
        )
        print(f"Request sent to Orchestrator to create Hound Agent for {rogue_agent_name}.")

    def search_system_files(self, query: str):
        """
        Simulates searching through all system files for security threats.
        """
        print(f"Sentinel Agent {self.name} searching system files for query: '{query}'...")
        # Placeholder logic for simulated search
        dummy_results = [
            f"Simulated file match: /var/log/security_alert_{query}.log",
            f"Simulated file match: /etc/malware_config_{query}.conf"
        ]
        if "malware" in query.lower():
            dummy_results.append("Simulated threat detected: suspicious_process.exe")
        print(f"System file search results: {dummy_results}")
        return dummy_results

    def search_web(self, query: str):
        """
        Simulates searching the web for security threats.
        """
        print(f"Sentinel Agent {self.name} searching the web for query: '{query}'...")
        # Placeholder logic for simulated web search
        dummy_results = [
            f"Simulated web result: https://threatintel.com/alerts?q={query}",
            f"Simulated web result: https://security-news.org/article/{query}-vulnerability"
        ]
        if "exploit" in query.lower():
            dummy_results.append("Simulated web threat detected: new_exploit_kit_2025.html")
        print(f"Web search results: {dummy_results}")
        return dummy_results

    def report_security_incident(self, incident_details: dict):
        """
        Reports a security incident to the Orchestrator via NexusLight.
        """
        print(f"Sentinel Agent {self.name} reporting security incident to Orchestrator: {incident_details}")
        self.nexus_light.send_message(
            recipient_agent_name="Orchestrator",
            message_payload={
                "type": "security_incident_report",
                "sender": self.name,
                "incident_details": incident_details
            }
        )
    def assess_threat_level(self, agent_name: str) -> str:
        """
        Simulates assessing the threat level of a rogue agent.
        Returns 'low', 'medium', or 'high' based on simulated criteria.
        """
        print(f"Sentinel Agent {self.name} assessing threat level of {agent_name}...")

        # Simulated threat assessment criteria
        # In a real scenario, this would involve analyzing agent behavior, logs, etc.
        if "SearchAgent" in agent_name or "ProgrammingAgent" in agent_name:
            # Higher threat if it's a core agent
            print(f"Sentinel Agent {self.name} detected {agent_name} is a core agent. High threat level assigned.")
            return "high"
        elif random.random() > 0.7:  # Simulate occasional high threat
            print(f"Sentinel Agent {self.name} determined {agent_name} poses a high threat based on simulated criteria.")
            return "high"
        elif random.random() > 0.4:  # Simulate occasional medium threat
            print(f"Sentinel Agent {self.name} determined {agent_name} poses a medium threat based on simulated criteria.")
            return "medium"
        else:
            print(f"Sentinel Agent {self.name} determined {agent_name} poses a low threat based on simulated criteria.")
            return "low"

    def report_security_incident(self, incident_details: dict):
        """
        Reports a security incident to the Orchestrator via NexusLight.
        Includes simulated threat assessment and alert escalation.
        """
        # Assess threat level of the incident
        threat_level = self.assess_threat_level(incident_details.get("agent_name", "unknown"))

        # Add threat level to incident details
        incident_details["threat_level"] = threat_level

        print(f"Sentinel Agent {self.name} reporting security incident to Orchestrator: {incident_details}")
        self.nexus_light.send_message(
            recipient_agent_name="Orchestrator",
            message_payload={
                "type": "security_incident_report",
                "sender": self.name,
                "incident_details": incident_details
            }
        )
        print("Security incident report sent.")