from agent_base import AgentBase
from nexus_light import NexusLight
from rag_database import RAGDatabase

class CommunicationsAgent(AgentBase):
    """
    The Communications Agent facilitates external communication and interaction,
    primarily with external "Nexus" servers.
    """
    def __init__(self, name: str, orchestrator):
        super().__init__(name=name, orchestrator=orchestrator)
        self.nexus_api_details = None  # Placeholder for Nexus API details
        self.rag_database = RAGDatabase() # Initialize RAG database
        self.nexus_light = NexusLight(agent_name=name, orchestrator_ref=orchestrator) # Initialize NexusLight

        # Store initial communication protocols and patterns
        self.rag_database.add_document("protocol_v1", "Standard communication protocol for Nexus server interaction: JSON over HTTPS.")
        self.rag_database.add_document("interaction_pattern_query", "Common interaction pattern: Send query, await JSON response, validate schema.")

    def contact_nexus_server(self, server_address: str):
        """
        Simulated method for contacting an external Nexus server.
        """
        print(f"CommunicationsAgent: Simulating contact with Nexus server at {server_address}...")
        # Simulate API call setup
        if self.nexus_api_details:
            print(f"CommunicationsAgent: Using API details: {self.nexus_api_details}")
        return {"status": "connected", "server": server_address}

    def interact_with_nexus_server(self, server_address: str, message: str):
        """
        Simulated method for interacting with an external Nexus server.
        """
        print(f"CommunicationsAgent: Simulating interaction with Nexus server at {server_address} with message: '{message}'...")
        # Simulate sending data and receiving a dummy response
        protocol_info = self.rag_database.retrieve_information("protocol_v1")
        print(f"CommunicationsAgent: Applying protocol: {protocol_info}")
        return {"status": "success", "response": f"Dummy response from {server_address} for '{message}'"}

    def handle_external_data_exchange(self, data_type: str, data: dict):
        """
        Simulates acting as a controlled gateway for external data exchange.
        Includes a placeholder for validation and routing logic.
        """
        print(f"CommunicationsAgent: Handling external data exchange for type '{data_type}' with data: {data}")
        # Placeholder for validation logic
        if data_type == "critical_update" and not data.get("signature_verified"):
            print("CommunicationsAgent: WARNING - Critical update received without signature verification. Blocking.")
            return {"status": "blocked", "reason": "Signature not verified"}

        # Placeholder for routing logic
        if data_type == "agent_message":
            target_agent = data.get("target_agent")
            message_content = data.get("message_content")
            if target_agent and message_content:
                print(f"CommunicationsAgent: Routing external message to {target_agent}.")
                self.facilitate_inter_agent_communication(target_agent, message_content)
                return {"status": "routed", "target": target_agent}
            else:
                print("CommunicationsAgent: Invalid agent message format.")
                return {"status": "failed", "reason": "Invalid agent message format"}
        
        print(f"CommunicationsAgent: Data exchange for '{data_type}' processed.")
        return {"status": "processed", "data_type": data_type}

    def facilitate_inter_agent_communication(self, target_agent_name: str, message_content: str):
        """
        Facilitates communication between internal agents using NexusLight.
        """
        print(f"CommunicationsAgent: Facilitating inter-agent communication to {target_agent_name} via NexusLight...")
        message_payload = {
            "type": "inter_agent_message",
            "content": message_content,
            "sender": self.name
        }
        self.nexus_light.send_message(target_agent_name, message_payload)
        print(f"CommunicationsAgent: Message sent to {target_agent_name}: '{message_content}'")

    def request_orchestrator_task(self):
        """
        Requests a task from the Orchestrator via NexusLight.
        """
        print(f"CommunicationsAgent: Requesting task from Orchestrator...")
        response = self.nexus_light.query_orchestrator_for_directives()
        if response.get("type") == "task_assigned":
            print(f"CommunicationsAgent: Received task from Orchestrator: {response.get('task')}")
            return response.get("task")
        else:
            print(f"CommunicationsAgent: Orchestrator response: {response.get('content')}")
            return None

    def report_task_status_to_orchestrator(self, task_id: str, status: str, details: dict = None):
        """
        Reports task completion or status to the Orchestrator via NexusLight.
        """
        print(f"CommunicationsAgent: Reporting task status '{status}' for task '{task_id}' to Orchestrator...")
        message_payload = {
            "type": "task_status_report",
            "content": {
                "task_id": task_id,
                "status": status,
                "details": details if details else {}
            },
            "sender": self.name
        }
        self.nexus_light.send_message("Orchestrator", message_payload)
        print(f"CommunicationsAgent: Task status reported for task '{task_id}'.")