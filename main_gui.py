import sys
from PyQt5.QtWidgets import (
    <PERSON><PERSON><PERSON><PERSON>, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QScrollArea, QFrame, QSizePolicy, QTabWidget, QCheckBox, QLineEdit,
    QTextEdit, QPlainTextEdit, # Added QPlainTextEdit
    QListWidget, QListWidgetItem, QComboBox, QTableWidget, QProgressBar, QSlider
)
from PyQt5.QtCore import Qt, QTimer, QUrl # QTimer might be useful for future real-time updates, though not strictly needed for this task
from PyQt5.QtGui import QTextOption
import json # For JSON formatting of errors

class AIAgentArmyGUI(QWidget):
    def __init__(self, orchestrator_ref=None):
        super().__init__()
        self.orchestrator_ref = orchestrator_ref
        self.agent_widgets = {} # To store references to agent monitoring widgets for updates

        self.setWindowTitle("AI Agent Army Control Panel")
        self.setGeometry(100, 100, 1200, 800) # Initial window size

        self.apply_dark_theme()
        self.init_ui()

    def apply_dark_theme(self):
        """Applies a dark theme to the GUI."""
        self.setStyleSheet("""
            QWidget {
                background-color: #2b2b2b;
                color: #f0f0f0;
                font-family: Arial, sans-serif;
                font-size: 14px;
            }
            QLabel {
                color: #f0f0f0;
            }
            QFrame {
                background-color: #3c3c3c;
                border: 1px solid #555555;
                border-radius: 5px;
            }
            QTextEdit {
                background-color: #1e1e1e;
                color: #d4d4d4;
                border: 1px solid #555555;
                border-radius: 3px;
                padding: 5px;
            }
            QPushButton {
                background-color: #007acc;
                color: #ffffff;
                border: none;
                border-radius: 3px;
                padding: 8px 15px;
            }
            QPushButton:hover {
                background-color: #005f99;
            }
            QScrollArea {
                border: none;
            }
        """)

    def init_ui(self):
        """Initializes the main layout and components of the GUI."""
        main_layout = QHBoxLayout()
        self.setLayout(main_layout)

        # Left Panel: System Overview and Agent Monitoring
        left_panel = QVBoxLayout()
        left_panel.setContentsMargins(10, 10, 5, 10) # Adjust margins
        main_layout.addLayout(left_panel, 2) # 20% width

        # System Overview Pane
        system_overview_frame = QFrame(self)
        system_overview_frame.setFrameShape(QFrame.StyledPanel)
        system_overview_frame.setFrameShadow(QFrame.Raised)
        system_overview_layout = QVBoxLayout(system_overview_frame)
        system_overview_layout.setContentsMargins(10, 10, 10, 10)
        system_overview_layout.addWidget(QLabel("<h2>System Overview</h2>"))

        self.cpu_usage_label = QLabel("Overall CPU Usage: N/A")
        self.memory_usage_label = QLabel("Memory Usage: N/A")
        self.nvme_swap_label = QLabel("NVMe Swap Usage: N/A")
        self.gpu_usage_label = QLabel("GPU Utilization: N/A (Placeholder)")
        self.network_usage_label = QLabel("Network Usage: In N/A / Out N/A")
        self.system_uptime_label = QLabel("System Uptime: N/A")
        self.total_agents_count_label = QLabel("Total Agents: 0") # Renamed for clarity

        system_overview_layout.addWidget(self.cpu_usage_label)
        system_overview_layout.addWidget(self.memory_usage_label)
        system_overview_layout.addWidget(self.nvme_swap_label)
        system_overview_layout.addWidget(self.gpu_usage_label)
        system_overview_layout.addWidget(self.network_usage_label)
        system_overview_layout.addWidget(self.system_uptime_label)
        system_overview_layout.addWidget(self.total_agents_count_label) # Add to system overview

        left_panel.addWidget(system_overview_frame)

        # Agent Monitoring Dashboard
        self.agent_monitoring_frame = QFrame(self)
        self.agent_monitoring_frame.setFrameShape(QFrame.StyledPanel)
        self.agent_monitoring_frame.setFrameShadow(QFrame.Raised)
        agent_monitoring_layout = QVBoxLayout(self.agent_monitoring_frame)
        agent_monitoring_layout.setContentsMargins(10, 10, 10, 10)
        
        agent_monitoring_layout.addWidget(QLabel("<h2>Agent Monitoring Dashboard</h2>"))
        
        # Placeholder for dynamic agent content
        self.agent_list_layout = QVBoxLayout()
        self.agent_list_layout.setAlignment(Qt.AlignTop) # Align content to the top
        
        agent_monitoring_scroll_area = QScrollArea(self)
        agent_monitoring_scroll_area.setWidgetResizable(True)
        agent_monitoring_widget = QWidget()
        agent_monitoring_widget.setLayout(self.agent_list_layout)
        agent_monitoring_scroll_area.setWidget(agent_monitoring_widget)
        
        agent_monitoring_layout.addWidget(agent_monitoring_scroll_area)
        left_panel.addWidget(self.agent_monitoring_frame)

        # Agent Count Display (moved to system overview, but keeping this for search agents if needed)
        self.agent_count_label = QLabel("Search Agents: 0") # Only search agents here now
        self.agent_count_label.setAlignment(Qt.AlignCenter)
        left_panel.addWidget(self.agent_count_label)

        # Agent Scaling Controls
        scaling_frame = QFrame(self)
        scaling_frame.setFrameShape(QFrame.StyledPanel)
        scaling_frame.setFrameShadow(QFrame.Raised)
        scaling_layout = QVBoxLayout(scaling_frame)
        scaling_layout.setContentsMargins(10, 10, 10, 10)
        scaling_layout.addWidget(QLabel("<h3>Adjust Agent Count</h3>"))

        self.desired_agents_input = QTextEdit(self)
        self.desired_agents_input.setPlaceholderText("Enter desired total agents (e.g., 5, 10)")
        self.desired_agents_input.setFixedHeight(30) # Make it a single line input
        scaling_layout.addWidget(self.desired_agents_input)

        adjust_button = QPushButton("Adjust Agents")
        adjust_button.clicked.connect(self.on_adjust_agents_clicked)
        scaling_layout.addWidget(adjust_button)

        left_panel.addWidget(scaling_frame)

        # Right Panel: Chat Interface and Error Display
        # Right Side Layout (holds the sub-agent lock and the main tab widget)
        right_side_layout = QVBoxLayout()
        right_side_layout.setContentsMargins(5, 10, 10, 10) # Adjust margins
        main_layout.addLayout(right_side_layout, 3) # 30% width

        # Sub-Agent Creation Lock Toggle (moved to top of right side)
        sub_agent_lock_frame = QFrame(self)
        sub_agent_lock_frame.setFrameShape(QFrame.StyledPanel)
        sub_agent_lock_frame.setFrameShadow(QFrame.Raised)
        sub_agent_lock_layout = QHBoxLayout(sub_agent_lock_frame)
        sub_agent_lock_layout.setContentsMargins(10, 10, 10, 10)
        sub_agent_lock_layout.addWidget(QLabel("<h3>Sub-Agent Creation:</h3>"))
        self.sub_agent_lock_toggle = QCheckBox("Allowed")
        self.sub_agent_lock_toggle.setStyleSheet("QCheckBox::indicator { width: 20px; height: 20px; } QCheckBox::indicator:checked { background-color: #4CAF50; } QCheckBox::indicator:unchecked { background-color: #cc0000; }")
        self.sub_agent_lock_toggle.setChecked(False) # Default to locked (red)
        self.sub_agent_lock_toggle.stateChanged.connect(self.on_sub_agent_lock_toggled)
        sub_agent_lock_layout.addWidget(self.sub_agent_lock_toggle)
        sub_agent_lock_layout.addStretch(1) # Push toggle to the left
        right_side_layout.addWidget(sub_agent_lock_frame)

        # Main Tab Widget for Right Panel Content
        self.main_content_tabs = QTabWidget(self)
        self.main_content_tabs.setStyleSheet("""
            QTabWidget::pane { border: 1px solid #555555; border-radius: 5px; }
            QTabBar::tab {
                background: #3c3c3c;
                color: #f0f0f0;
                padding: 8px 15px;
                border: 1px solid #555555;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background: #2b2b2b;
                border-color: #007acc;
                border-bottom-color: #2b2b2b;
            }
            QTextEdit, QLineEdit, QListWidget, QTableWidget, QComboBox {
                background-color: #1e1e1e;
                color: #d4d4d4;
                border: 1px solid #555555;
                border-radius: 3px;
                padding: 5px;
            }
            QComboBox::drop-down {
                border: 0px; /* No border for the arrow */
            }
            QComboBox::down-arrow {
                image: url(./icons/down_arrow.png); /* Placeholder for a custom arrow icon */
            }
            QSlider::groove:horizontal {
                border: 1px solid #555555;
                height: 8px;
                background: #3c3c3c;
                margin: 2px 0;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: #007acc;
                border: 1px solid #007acc;
                width: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }
            QProgressBar {
                border: 1px solid #555555;
                border-radius: 5px;
                text-align: center;
                color: #f0f0f0;
                background-color: #3c3c3c;
            }
            QProgressBar::chunk {
                background-color: #007acc;
                border-radius: 5px;
            }
        """)
        right_side_layout.addWidget(self.main_content_tabs, 1) # Give it all remaining space

        # Add existing sections as tabs to self.main_content_tabs
        # Chat Interface Section (self.chat_tabs is already a QTabWidget, so it will be a tab within a tab widget)
        self.main_content_tabs.addTab(self.chat_tabs, "Communications")

        # Interactive Terminal Section
        terminal_widget = QWidget()
        terminal_layout = QVBoxLayout(terminal_widget)
        terminal_layout.setContentsMargins(10, 10, 10, 10)
        terminal_layout.addWidget(QLabel("<h2>Interactive Terminal</h2>"))
        self.terminal_output = QPlainTextEdit(self) # Replaced QTextEdit with QPlainTextEdit
        self.terminal_output.setReadOnly(True)
        self.terminal_output.setPlaceholderText("System output and agent narratives will appear here...")
        terminal_layout.addWidget(self.terminal_output)

        self.terminal_input = QLineEdit(self) # Re-parenting
        self.terminal_input.setPlaceholderText("Enter command...")
        self.terminal_input.returnPressed.connect(self.on_terminal_input_entered)
        terminal_layout.addWidget(self.terminal_input)
        self.main_content_tabs.addTab(terminal_widget, "Terminal")

        # Error Display Section
        error_widget = QWidget()
        error_layout = QVBoxLayout(error_widget)
        error_layout.setContentsMargins(10, 10, 10, 10)
        error_layout.addWidget(QLabel("<h2>Error Logging & Auto-Fix Panel</h2>"))
        self.error_list_widget = QListWidget(self) # Re-parenting
        error_layout.addWidget(self.error_list_widget)
        self.main_content_tabs.addTab(error_widget, "Errors")

        # Code Review & Sandbox Visualization Section
        code_review_widget = QWidget()
        code_review_layout = QVBoxLayout(code_review_widget)
        code_review_layout.setContentsMargins(10, 10, 10, 10)
        code_review_layout.addWidget(QLabel("<h2>Code Review & Sandbox Visualization</h2>"))

        # Sandbox Performance Display
        sandbox_perf_group = QFrame(self) # Re-parenting
        sandbox_perf_group.setStyleSheet("QFrame { background-color: #333333; border: 1px solid #444444; border-radius: 5px; padding: 5px; }")
        sandbox_perf_layout = QVBoxLayout(sandbox_perf_group)
        sandbox_perf_layout.addWidget(QLabel("<h3>Sandbox Performance:</h3>"))
        self.sandbox_latency_label = QLabel("Latency Impact: N/A")
        self.sandbox_cpu_label = QLabel("Resource Load (CPU): N/A")
        self.sandbox_ram_label = QLabel("Resource Load (RAM): N/A")
        self.sandbox_status_label = QLabel("Test Status: N/A")
        sandbox_perf_layout.addWidget(self.sandbox_latency_label)
        sandbox_perf_layout.addWidget(self.sandbox_cpu_label)
        sandbox_perf_layout.addWidget(self.sandbox_ram_label)
        sandbox_perf_layout.addWidget(self.sandbox_status_label)
        code_review_layout.addWidget(sandbox_perf_group)

        # Revert/Rollback Function
        revert_button = QPushButton("Revert to Last Stable") # Re-parenting
        revert_button.setStyleSheet("QPushButton { background-color: #d35400; } QPushButton:hover { background-color: #e67e22; }")
        revert_button.clicked.connect(self.on_revert_button_clicked)
        code_review_layout.addWidget(revert_button)

        # Git Integration (Placeholder)
        git_status_group = QFrame(self) # Re-parenting
        git_status_group.setStyleSheet("QFrame { background-color: #333333; border: 1px solid #444444; border-radius: 5px; padding: 5px; }")
        git_status_layout = QVBoxLayout(git_status_group)
        git_status_layout.addWidget(QLabel("<h3>Git Status:</h3>"))
        self.git_status_display = QTextEdit(self) # Re-parenting
        self.git_status_display.setReadOnly(True)
        self.git_status_display.setPlaceholderText("Simulated Git status will appear here...")
        self.git_status_display.setFixedHeight(100) # Limit height
        git_status_layout.addWidget(self.git_status_display)
        code_review_layout.addWidget(git_status_group)
        self.main_content_tabs.addTab(code_review_widget, "Code & Sandbox")

        # Add new panels
        self.main_content_tabs.addTab(self._create_settings_panel(), "Settings")
        self.main_content_tabs.addTab(self._create_task_management_panel(), "Tasks")

    def _create_settings_panel(self):
        """
        Creates and returns the Settings & Configuration Panel widget.
        """
        settings_widget = QWidget()
        settings_layout = QVBoxLayout(settings_widget)
        settings_layout.setContentsMargins(10, 10, 10, 10)
        settings_layout.addWidget(QLabel("<h2>Settings & Configuration</h2>"))

        # API Key Management
        api_key_group = QFrame(self)
        api_key_group.setFrameShape(QFrame.StyledPanel)
        api_key_group.setFrameShadow(QFrame.Raised)
        api_key_layout = QVBoxLayout(api_key_group)
        api_key_layout.addWidget(QLabel("<h3>API Key Management (Simulated)</h3>"))
        
        api_key_input_layout = QHBoxLayout()
        self.api_key_line_edit = QLineEdit(self)
        self.api_key_line_edit.setPlaceholderText("Enter API Key")
        api_key_input_layout.addWidget(self.api_key_line_edit)
        
        add_key_button = QPushButton("Add Key")
        add_key_button.clicked.connect(self._simulate_add_api_key)
        api_key_input_layout.addWidget(add_key_button)
        api_key_layout.addLayout(api_key_input_layout)

        self.api_key_list_widget = QListWidget(self)
        self.api_key_list_widget.addItem("Simulated_API_Key_1 (Active)")
        self.api_key_list_widget.addItem("Simulated_API_Key_2 (Inactive)")
        api_key_layout.addWidget(self.api_key_list_widget)

        key_buttons_layout = QHBoxLayout()
        edit_key_button = QPushButton("Edit Selected")
        edit_key_button.clicked.connect(self._simulate_edit_api_key)
        key_buttons_layout.addWidget(edit_key_button)
        delete_key_button = QPushButton("Delete Selected")
        delete_key_button.clicked.connect(self._simulate_delete_api_key)
        key_buttons_layout.addWidget(delete_key_button)
        api_key_layout.addLayout(key_buttons_layout)
        settings_layout.addWidget(api_key_group)

        # Language Model Selection
        llm_selection_group = QFrame(self)
        llm_selection_group.setFrameShape(QFrame.StyledPanel)
        llm_selection_group.setFrameShadow(QFrame.Raised)
        llm_selection_layout = QVBoxLayout(llm_selection_group)
        llm_selection_layout.addWidget(QLabel("<h3>Language Model Selection (Simulated)</h3>"))
        
        llm_selection_layout.addWidget(QLabel("Primary Agent LLM:"))
        self.primary_llm_combo = QComboBox(self)
        self.primary_llm_combo.addItems(["Gemini-Pro", "GPT-4o", "Claude 3 Opus", "Llama 3 (Local)"])
        llm_selection_layout.addWidget(self.primary_llm_combo)

        llm_selection_layout.addWidget(QLabel("Sub-Agent Default/Override (Placeholder):"))
        self.sub_agent_llm_combo = QComboBox(self)
        self.sub_agent_llm_combo.addItems(["Inherit from Parent", "Gemini-Pro", "GPT-4o Mini"])
        llm_selection_layout.addWidget(self.sub_agent_llm_combo)
        settings_layout.addWidget(llm_selection_group)

        # Theme Customization
        theme_group = QFrame(self)
        theme_group.setFrameShape(QFrame.StyledPanel)
        theme_group.setFrameShadow(QFrame.Raised)
        theme_layout = QVBoxLayout(theme_group)
        theme_layout.addWidget(QLabel("<h3>Theme Customization (Simulated)</h3>"))
        
        color_picker_button = QPushButton("Choose Accent Color")
        color_picker_button.clicked.connect(self._simulate_color_picker)
        theme_layout.addWidget(color_picker_button)

        theme_layout.addWidget(QLabel("Dark Mode Intensity:"))
        self.dark_mode_slider = QSlider(Qt.Horizontal)
        self.dark_mode_slider.setRange(0, 100)
        self.dark_mode_slider.setValue(80) # Default dark intensity
        self.dark_mode_slider.setTickPosition(QSlider.TicksBelow)
        self.dark_mode_slider.setTickInterval(10)
        theme_layout.addWidget(self.dark_mode_slider)
        settings_layout.addWidget(theme_group)

        # Persistence Settings, Log Configuration, Notification Preferences
        other_settings_group = QFrame(self)
        other_settings_group.setFrameShape(QFrame.StyledPanel)
        other_settings_group.setFrameShadow(QFrame.Raised)
        other_settings_layout = QVBoxLayout(other_settings_group)
        other_settings_layout.addWidget(QLabel("<h3>Other Settings (Placeholders)</h3>"))
        
        self.auto_save_checkbox = QCheckBox("Enable Auto-Save")
        self.manual_save_button = QPushButton("Manual Save Now")
        self.backup_settings_button = QPushButton("Create Backup")
        other_settings_layout.addWidget(self.auto_save_checkbox)
        other_settings_layout.addWidget(self.manual_save_button)
        other_settings_layout.addWidget(self.backup_settings_button)

        other_settings_layout.addWidget(QLabel("Log Verbosity:"))
        self.log_verbosity_combo = QComboBox(self)
        self.log_verbosity_combo.addItems(["Debug", "Info", "Warning", "Error", "Critical"])
        other_settings_layout.addWidget(self.log_verbosity_combo)

        self.notify_errors_checkbox = QCheckBox("Notify on Errors")
        self.notify_status_checkbox = QCheckBox("Notify on Agent Status Changes")
        other_settings_layout.addWidget(self.notify_errors_checkbox)
        other_settings_layout.addWidget(self.notify_status_checkbox)
        settings_layout.addWidget(other_settings_group)

        # Save/Load Buttons
        settings_buttons_layout = QHBoxLayout()
        save_settings_button = QPushButton("Save Settings")
        save_settings_button.clicked.connect(self._simulate_save_settings)
        settings_buttons_layout.addWidget(save_settings_button)
        
        load_settings_button = QPushButton("Load Settings")
        load_settings_button.clicked.connect(self._simulate_load_settings)
        settings_buttons_layout.addWidget(load_settings_button)
        settings_layout.addLayout(settings_buttons_layout)

        settings_layout.addStretch(1) # Push content to top
        return settings_widget

    def _create_task_management_panel(self):
        """
        Creates and returns the Task & Project Management View widget.
        """
        task_widget = QWidget()
        task_layout = QVBoxLayout(task_widget)
        task_layout.setContentsMargins(10, 10, 10, 10)
        task_layout.addWidget(QLabel("<h2>Task & Project Management</h2>"))

        # New Directive Input
        directive_group = QFrame(self)
        directive_group.setFrameShape(QFrame.StyledPanel)
        directive_group.setFrameShadow(QFrame.Raised)
        directive_layout = QVBoxLayout(directive_group)
        directive_layout.addWidget(QLabel("<h3>New Directive Input:</h3>"))
        
        self.new_directive_text_edit = QTextEdit(self)
        self.new_directive_text_edit.setPlaceholderText("Enter new high-level directive for the AI Agent Army...")
        self.new_directive_text_edit.setFixedHeight(80)
        directive_layout.addWidget(self.new_directive_text_edit)
        
        submit_directive_button = QPushButton("Submit Directive")
        submit_directive_button.clicked.connect(self._on_submit_directive_clicked)
        directive_layout.addWidget(submit_directive_button)
        task_layout.addWidget(directive_group)

        # Interactive Task Board
        task_board_group = QFrame(self)
        task_board_group.setFrameShape(QFrame.StyledPanel)
        task_board_group.setFrameShadow(QFrame.Raised)
        task_board_layout = QVBoxLayout(task_board_group)
        task_board_layout.addWidget(QLabel("<h3>Interactive Task Board (Simulated)</h3>"))
        
        self.task_table_widget = QTableWidget(self)
        self.task_table_widget.setColumnCount(5)
        self.task_table_widget.setHorizontalHeaderLabels(["Task ID", "Description", "Assigned Agent", "Status", "Progress"])
        self.task_table_widget.horizontalHeader().setStretchLastSection(True)
        self.task_table_widget.setEditTriggers(QTableWidget.NoEditTriggers) # Make table read-only
        task_board_layout.addWidget(self.task_table_widget)
        task_layout.addWidget(task_board_group)

        # Progress Monitoring
        progress_group = QFrame(self)
        progress_group.setFrameShape(QFrame.StyledPanel)
        progress_group.setFrameShadow(QFrame.Raised)
        progress_layout = QVBoxLayout(progress_group)
        progress_layout.addWidget(QLabel("<h3>Overall Project Progress:</h3>"))
        
        self.overall_progress_bar = QProgressBar(self)
        self.overall_progress_bar.setValue(0)
        progress_layout.addWidget(self.overall_progress_bar)
        
        self.overall_progress_label = QLabel("Current Progress: 0%")
        progress_layout.addWidget(self.overall_progress_label)
        task_layout.addWidget(progress_group)

        task_layout.addStretch(1) # Push content to top
        return task_widget

    def _simulate_add_api_key(self):
        key = self.api_key_line_edit.text().strip()
        if key:
            self.api_key_list_widget.addItem(f"Simulated_Key: {key[:10]}... (Active)")
            self.api_key_line_edit.clear()
            self.append_terminal_output(f"Simulated: Added API Key '{key[:10]}...' to management.", output_type="System")
        else:
            self.append_terminal_output("Please enter an API key to add.", output_type="Error")

    def _simulate_edit_api_key(self):
        selected_items = self.api_key_list_widget.selectedItems()
        if selected_items:
            current_text = selected_items[0].text()
            self.append_terminal_output(f"Simulated: Editing API Key '{current_text}'. (Not implemented)", output_type="System")
        else:
            self.append_terminal_output("Please select an API key to edit.", output_type="Error")

    def _simulate_delete_api_key(self):
        selected_items = self.api_key_list_widget.selectedItems()
        if selected_items:
            row = self.api_key_list_widget.row(selected_items[0])
            item_text = self.api_key_list_widget.item(row).text()
            self.api_key_list_widget.takeItem(row)
            self.append_terminal_output(f"Simulated: Deleted API Key '{item_text}'.", output_type="System")
        else:
            self.append_terminal_output("Please select an API key to delete.", output_type="Error")

    def _simulate_color_picker(self):
        self.append_terminal_output("Simulated: Opening color picker for accent color. (Not implemented)", output_type="System")

    def _simulate_save_settings(self):
        self.append_terminal_output("Simulated: Saving all settings. (Not implemented)", output_type="System")
        # In a real scenario, you'd gather all current settings from widgets and save them.
        # Example:
        # settings = {
        #     "primary_llm": self.primary_llm_combo.currentText(),
        #     "auto_save": self.auto_save_checkbox.isChecked(),
        #     # ... other settings
        # }
        # self.orchestrator_ref.save_settings(settings) # Call orchestrator to save

    def _simulate_load_settings(self):
        self.append_terminal_output("Simulated: Loading all settings. (Not implemented)", output_type="System")
        # In a real scenario, you'd load settings and update widgets.
        # Example:
        # loaded_settings = self.orchestrator_ref.load_settings()
        # if loaded_settings:
        #     self.primary_llm_combo.setCurrentText(loaded_settings.get("primary_llm", "Gemini-Pro"))
        #     self.auto_save_checkbox.setChecked(loaded_settings.get("auto_save", False))
        #     # ... update other widgets

    def _on_submit_directive_clicked(self):
        directive_text = self.new_directive_text_edit.toPlainText().strip()
        if directive_text:
            self.append_terminal_output(f"User submitted new directive: '{directive_text}'", output_type="System")
            if self.orchestrator_ref:
                self.orchestrator_ref.receive_user_directive(directive_text)
                self.new_directive_text_edit.clear()
            else:
                self.append_terminal_output("Orchestrator not connected. Cannot submit directive.", output_type="Error")
        else:
            self.append_terminal_output("Please enter a directive before submitting.", output_type="Error")

    def update_task_board(self, tasks: list):
        """
        Updates the interactive task board with the given list of tasks.
        Each task is a dictionary with keys: 'task_id', 'description', 'assigned_to', 'status', 'progress'.
        """
        self.task_table_widget.setRowCount(len(tasks))
        for row, task in enumerate(tasks):
            self.task_table_widget.setItem(row, 0, QTableWidgetItem(task.get("task_id", "N/A")))
            self.task_table_widget.setItem(row, 1, QTableWidgetItem(task.get("description", "N/A")))
            self.task_table_widget.setItem(row, 2, QTableWidgetItem(task.get("assigned_to", "N/A")))
            self.task_table_widget.setItem(row, 3, QTableWidgetItem(task.get("status", "N/A")))
            self.task_table_widget.setItem(row, 4, QTableWidgetItem(str(task.get("progress", "0%"))))
        self.task_table_widget.resizeColumnsToContents()

    def update_overall_progress(self, progress_percentage: int):
        """
        Updates the overall project progress bar and label.
        """
        self.overall_progress_bar.setValue(progress_percentage)
        self.overall_progress_label.setText(f"Current Progress: {progress_percentage}%")

    def add_agent_monitoring_row(self, agent_name: str):
        """
        Dynamically adds a new row for agent monitoring to the dashboard.
        Each row displays agent name, running time, CPU/memory usage, and a kill switch button.
        """
        agent_widget = QFrame(self)
        agent_widget.setFrameShape(QFrame.Box)
        agent_widget.setFrameShadow(QFrame.Sunken)
        agent_widget.setStyleSheet("QFrame { background-color: #2d2d2d; border: 1px solid #444444; margin-bottom: 5px; }")
        
        agent_layout = QVBoxLayout(agent_widget)
        agent_layout.setContentsMargins(5, 5, 5, 5)

        name_label = QLabel(f"<b>Agent Name:</b> {agent_name}")
        running_time_label = QLabel("Running Time: N/A")
        cpu_usage_label = QLabel("CPU Usage: N/A")
        memory_usage_label = QLabel("Memory Usage: N/A")
        network_usage_label = QLabel("Network Usage: N/A") # New label for individual agent network usage
        bottleneck_label = QLabel("Bottleneck: None") # New label for bottleneck visualization
        bottleneck_label.setStyleSheet("color: #f0f0f0;") # Default color

        agent_layout.addWidget(name_label)
        agent_layout.addWidget(running_time_label)
        agent_layout.addWidget(cpu_usage_label)
        agent_layout.addWidget(memory_usage_label)
        agent_layout.addWidget(network_usage_label) # Add new label
        agent_layout.addWidget(bottleneck_label) # Add new bottleneck label

        kill_switch_button = QPushButton("Kill Switch")
        kill_switch_button.setStyleSheet("QPushButton { background-color: #cc0000; } QPushButton:hover { background-color: #990000; }")
        kill_switch_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        
        # Connect kill switch button to orchestrator
        if self.orchestrator_ref:
            kill_switch_button.clicked.connect(lambda: self.orchestrator_ref.access_kill_switch(agent_name))
        
        agent_layout.addWidget(kill_switch_button, alignment=Qt.AlignRight)
        
        self.agent_list_layout.addWidget(agent_widget)
        self.agent_widgets[agent_name] = {
            "widget": agent_widget,
            "name_label": name_label,
            "running_time_label": running_time_label,
            "cpu_usage_label": cpu_usage_label,
            "memory_usage_label": memory_usage_label,
            "network_usage_label": network_usage_label, # Add new label reference
            "bottleneck_label": bottleneck_label, # Add new bottleneck label reference
            "kill_button": kill_switch_button
        }

    def update_agent_performance_metrics(self, agent_name: str, performance_data: dict):
        """
        Updates the performance metrics for a specific agent in the monitoring dashboard.
        This method is called by the Orchestrator.
        """
        if agent_name in self.agent_widgets:
            agent_info = self.agent_widgets[agent_name]
            agent_info["cpu_usage_label"].setText(f"CPU Usage: {performance_data.get('agent_cpu', 'N/A')}")
            agent_info["memory_usage_label"].setText(f"Memory Usage: {performance_data.get('agent_memory', 'N/A')}")
            agent_info["network_usage_label"].setText(f"Network Latency: {performance_data.get('network_latency', 'N/A')}")
            
            # Update bottleneck label based on performance data
            bottlenecks = []
            try:
                cpu_val = float(performance_data.get("agent_cpu", "0%").replace("%", ""))
                mem_val = float(performance_data.get("agent_memory", "0MB").replace("MB", ""))
                net_lat_val = float(performance_data.get("network_latency", "0ms").replace("ms", ""))

                if cpu_val > 15: # Example threshold
                    bottlenecks.append("High CPU")
                if mem_val > 300: # Example threshold
                    bottlenecks.append("High Memory")
                if net_lat_val > 150: # Example threshold
                    bottlenecks.append("High Network Latency")
            except ValueError:
                pass # Handle cases where data might be malformed

            if bottlenecks:
                agent_info["bottleneck_label"].setText(f"Bottleneck: {', '.join(bottlenecks)}")
                agent_info["bottleneck_label"].setStyleSheet("color: #ffcc00; font-weight: bold;") # Highlight in yellow/orange
            else:
                agent_info["bottleneck_label"].setText("Bottleneck: None")
                agent_info["bottleneck_label"].setStyleSheet("color: #f0f0f0;") # Reset to default

    def append_terminal_output(self, text: str, output_type: str = "Narrative"):
        """
        Formats and appends text to the interactive terminal display.
        output_type can be "Narrative", "Code Snippet", "System", "Error", etc.
        Implements a line limit for performance.
        """
        # Define a line limit
        max_lines = 1000

        # Format the text based on output_type (currently plain text due to QPlainTextEdit)
        # Note: With QPlainTextEdit, rich text formatting applied here will not be rendered.
        # For performance, we are prioritizing plain text appending.
        formatted_text = f"[{output_type}] {text}" # Simple prefix for context

        # Append the new text
        self.terminal_output.appendPlainText(formatted_text)

        # Remove oldest line if the limit is exceeded
        while self.terminal_output.blockCount() > max_lines:
            cursor = self.terminal_output.textCursor()
            cursor.movePosition(cursor.Start)
            cursor.select(cursor.BlockUnderCursor)
            cursor.removeSelectedText()
            # Remove the extra newline character that might remain
            cursor.deleteChar()

        # Auto-scroll to the bottom
        self.terminal_output.verticalScrollBar().setValue(self.terminal_output.verticalScrollBar().maximum())

    def on_terminal_input_entered(self):
        """
        Handles user input from the interactive terminal.
        Sends the command to the Orchestrator.
        """
        command = self.terminal_input.text().strip()
        if command:
            self.append_terminal_output(f"<span style='color: #007acc;'><b>User Input:</b> {command}</span>", output_type="System")
            if self.orchestrator_ref:
                self.orchestrator_ref.handle_user_command(command)
            else:
                self.append_terminal_output("Orchestrator not connected. Cannot process command.", output_type="Error")
            self.terminal_input.clear()

    def update_chat_display(self, sender: str, message: str, sender_hierarchy: list = None, recipient_name: str = None, recipient_hierarchy: list = None):
        """
        Updates the chat interface with a new message, including hierarchy information and improved formatting.
        """
        # Determine the display name for the sender
        sender_display = f"<b>{sender}</b>"
        if sender_hierarchy and len(sender_hierarchy) > 1:
            sender_display += f" (via {' -> '.join(sender_hierarchy[:-1])})"

        # Determine the display name for the recipient
        recipient_display = ""
        if recipient_name:
            recipient_display = f" to <b>{recipient_name}</b>"
            if recipient_hierarchy and len(recipient_hierarchy) > 1:
                recipient_display += f" (via {' -> '.join(recipient_hierarchy[:-1])})"
        
        # Construct the full message with sender, recipient, and content
        display_message = f"<p style='margin-bottom: 2px;'>{sender_display}{recipient_display}: {message}</p>"

        # Append to "All Chats"
        self.all_chats_display.append(display_message)

        # Append to "All Chats"
        self.all_chats_display.append(display_message)

        # Determine which other tabs to update based on sender/recipient roles and hierarchy
        # Orchestrator-Main Agent Chats
        is_orchestrator_main_chat = False
        if sender == "Orchestrator" and recipient_name and recipient_name not in ["Orchestrator"] and not (recipient_hierarchy and len(recipient_hierarchy) > 1):
            is_orchestrator_main_chat = True
        elif recipient_name == "Orchestrator" and sender and sender not in ["Orchestrator"] and not (sender_hierarchy and len(sender_hierarchy) > 1):
            is_orchestrator_main_chat = True
        
        if is_orchestrator_main_chat:
            self.orchestrator_main_chats_display.append(display_message)

        # Parent-Sub Agent Chats
        is_parent_sub_chat = False
        if sender_hierarchy and len(sender_hierarchy) > 1: # Sender is a sub-agent
            is_parent_sub_chat = True
        elif recipient_hierarchy and len(recipient_hierarchy) > 1: # Recipient is a sub-agent
            is_parent_sub_chat = True
        
        if is_parent_sub_chat:
            self.parent_sub_chats_display.append(display_message)

    def display_error(self, error_data: dict):
        """
        Displays an error in the QListWidget, with JSON formatting and an Auto-Fix button.
        error_data is expected to be a dictionary with keys like 'agent_name', 'message', 'details', 'fixed'.
        """
        agent_name = error_data.get("agent_name", "Unknown Agent")
        error_message = error_data.get("message", "No message provided.")
        details = error_data.get("details", {})
        fixed = error_data.get("fixed", False)
        auto_fix_available = error_data.get("auto_fix_available", False)

        error_item = QListWidgetItem(self.error_list_widget)
        error_widget = QWidget()
        error_layout = QVBoxLayout(error_widget)
        error_layout.setContentsMargins(5, 5, 5, 5)

        # Display error message and JSON details
        error_text = f"<b>ERROR from {agent_name}:</b> {error_message}"
        error_label = QLabel(error_text)
        error_label.setWordWrap(True)
        error_layout.addWidget(error_label)

        if details:
            details_json = json.dumps(details, indent=2)
            details_text_edit = QTextEdit()
            details_text_edit.setReadOnly(True)
            details_text_edit.setPlainText(details_json)
            details_text_edit.setFixedHeight(details_text_edit.document().size().height() + 10) # Adjust height dynamically
            details_text_edit.setStyleSheet("background-color: #1e1e1e; color: #d4d4d4; border: 1px solid #444444; border-radius: 3px; padding: 5px;")
            details_text_edit.setWordWrapMode(QTextOption.WrapAnywhere)
            error_layout.addWidget(details_text_edit)

        if fixed:
            fixed_label = QLabel("<span style='color: #4CAF50;'>[FIXED]</span>")
            error_layout.addWidget(fixed_label)
        elif auto_fix_available:
            auto_fix_button = QPushButton("Auto-Fix")
            auto_fix_button.setStyleSheet("QPushButton { background-color: #007acc; color: white; border: none; border-radius: 3px; padding: 5px 10px; } QPushButton:hover { background-color: #005f99; }")
            auto_fix_button.clicked.connect(lambda: self.on_auto_fix_button_clicked(error_data))
            error_layout.addWidget(auto_fix_button, alignment=Qt.AlignRight)

        error_item.setSizeHint(error_widget.sizeHint())
        self.error_list_widget.addItem(error_item)
        self.error_list_widget.setItemWidget(error_item, error_widget)
        self.error_list_widget.scrollToBottom()

    def on_auto_fix_button_clicked(self, error_data: dict):
        """Handles the click event for the Auto-Fix button."""
        if self.orchestrator_ref:
            agent_name = error_data.get("agent_name")
            error_message = error_data.get("message")
            details = error_data.get("details")
            self.orchestrator_ref.trigger_auto_fix(agent_name, error_message, details)
        else:
            print("GUI Error: Orchestrator reference not set for auto-fix.")

    def _handle_auto_fix_link_clicked(self, url):
        """This method is no longer needed as we are using QListWidget with QPushButton."""
        pass # Keep for now, but it's effectively deprecated by the new error display

    def _handle_auto_fix_link_clicked(self, url):
        """Handles clicks on the auto-fix link in the error display."""
        url_str = url.toString()
        if url_str.startswith("auto_fix_trigger:"):
            parts = url_str.split(":")
            if len(parts) >= 4:
                agent_name = parts[1]
                error_message = parts[2]
                # Reconstruct details from the rest of the parts, assuming it was a simple string representation
                details_str = ":".join(parts[3:])
                try:
                    details = eval(details_str) # Use eval to convert string back to dict (be cautious with eval in real apps)
                except:
                    details = {"raw_details": details_str} # Fallback if eval fails

                if self.orchestrator_ref:
                    self.orchestrator_ref.trigger_auto_fix(agent_name, error_message, details)
                else:
                    print("GUI Error: Orchestrator reference not set for auto-fix.")
            else:
                print(f"GUI Error: Malformed auto-fix URL: {url_str}")

    def update_kill_switch_status(self, agent_name: str, is_active: bool):
        """
        Visually reflects the agent's active state in the GUI.
        Changes the kill switch button color and text.
        """
        if agent_name in self.agent_widgets:
            kill_button = self.agent_widgets[agent_name]["kill_button"]
            if is_active:
                kill_button.setStyleSheet("QPushButton { background-color: #cc0000; } QPushButton:hover { background-color: #990000; }")
                kill_button.setText("Kill Switch")
            else:
                kill_button.setStyleSheet("QPushButton { background-color: #4CAF50; } QPushButton:hover { background-color: #388E3C; }")
                kill_button.setText("Agent Paused (Click to Kill)") # Or "Resume" if that functionality is added

    def update_system_metrics(self, cpu: str, memory: str, nvme: str, gpu: str, network_in: str, network_out: str, uptime: str):
        """Updates the system-wide metrics displayed on the main dashboard."""
        self.cpu_usage_label.setText(f"Overall CPU Usage: {cpu}")
        self.memory_usage_label.setText(f"Memory Usage: {memory}")
        self.nvme_swap_label.setText(f"NVMe Swap Usage: {nvme}")
        self.gpu_usage_label.setText(f"GPU Utilization: {gpu}")
        self.network_usage_label.setText(f"Network Usage: In {network_in} / Out {network_out}")
        self.system_uptime_label.setText(f"System Uptime: {uptime}")

    def update_agent_status(self, agent_name: str, running_time: str = None, cpu_usage: str = None, memory_usage: str = None, network_usage: str = None):
        """Updates the status of a specific agent in the monitoring dashboard."""
        if agent_name in self.agent_widgets:
            agent_info = self.agent_widgets[agent_name]
            if running_time:
                agent_info["running_time_label"].setText(f"Running Time: {running_time}")
            if cpu_usage:
                agent_info["cpu_usage_label"].setText(f"CPU Usage: {cpu_usage}")
            if memory_usage:
                agent_info["memory_usage_label"].setText(f"Memory Usage: {memory_usage}")
            if network_usage:
                agent_info["network_usage_label"].setText(f"Network Usage: {network_usage}")

    def update_agent_bottleneck_status(self, agent_name: str, bottleneck_details: str):
        """
        Updates the status of a specific agent to display identified bottlenecks.
        """
        if agent_name in self.agent_widgets:
            agent_info = self.agent_widgets[agent_name]
            # Add bottleneck information to the agent's status display
            agent_info["cpu_usage_label"].setStyleSheet("color: #ff0000;")  # Highlight in red
            agent_info["memory_usage_label"].setStyleSheet("color: #ff0000;")
            # Add a new label for bottleneck details if needed
            if hasattr(agent_info, "bottleneck_label"):
                agent_info["bottleneck_label"].setText(f"Bottleneck: {bottleneck_details}")
            else:
                bottleneck_label = QLabel(f"Bottleneck: {bottleneck_details}")
                bottleneck_label.setStyleSheet("color: #ff0000; font-weight: bold;")
                agent_info["agent_layout"].addWidget(bottleneck_label)
                agent_info["bottleneck_label"] = bottleneck_label

    def update_agent_counts(self, total_agents: int, search_agents: int):
        """Updates the display for total and search agent counts."""
        self.total_agents_count_label.setText(f"Total Agents: {total_agents}") # Update total agents in system overview
        self.agent_count_label.setText(f"Search Agents: {search_agents}") # Update search agents in agent monitoring section

    def remove_agent_monitoring_row(self, agent_name: str):
        """Removes an agent's monitoring row from the dashboard."""
        if agent_name in self.agent_widgets:
            widget_to_remove = self.agent_widgets[agent_name]["widget"]
            self.agent_list_layout.removeWidget(widget_to_remove)
            widget_to_remove.deleteLater()
            del self.agent_widgets[agent_name]

    def on_adjust_agents_clicked(self):
        """Handles the click event for the Adjust Agents button."""
        if self.orchestrator_ref:
            try:
                desired_agents_text = self.desired_agents_input.toPlainText().strip()
                if not desired_agents_text:
                    self.update_error_display("GUI", "Please enter a desired agent count.")
                    return

                desired_total_agents = int(desired_agents_text)
                self.orchestrator_ref.adjust_agent_count(desired_total_agents)
            except ValueError:
                self.update_error_display("GUI", "Invalid input. Please enter a number for desired agents.")
            except Exception as e:
                self.update_error_display("GUI", f"An error occurred during agent adjustment: {e}")
        else:
            self.update_error_display("GUI", "Orchestrator reference not set. Cannot adjust agents.")

    def update_sub_agent_lock_status(self, status: bool):
        """
        Updates the visual state of the sub-agent creation lock toggle.
        """
        self.sub_agent_lock_toggle.setChecked(status)
        # The stylesheet handles the color change based on checked state

    def update_sandbox_performance(self, latency: str, cpu: str, ram: str, status: str):
        """
        Updates the sandbox performance display labels.
        """
        self.sandbox_latency_label.setText(f"Latency Impact: {latency}")
        self.sandbox_cpu_label.setText(f"Resource Load (CPU): {cpu}")
        self.sandbox_ram_label.setText(f"Resource Load (RAM): {ram}")
        self.sandbox_status_label.setText(f"Test Status: {status}")

    def on_revert_button_clicked(self):
        """
        Handles the click event for the Revert to Last Stable button.
        """
        if self.orchestrator_ref:
            self.orchestrator_ref.revert_codebase()
            self.append_terminal_output("Revert to Last Stable initiated by user.", output_type="System")
        else:
            self.append_terminal_output("Orchestrator not connected. Cannot revert codebase.", output_type="Error")

    def update_git_status(self, status_text: str):
        """
        Updates the Git status display.
        """
        self.git_status_display.setPlainText(status_text)

    def on_sub_agent_lock_toggled(self, state):
        """
        Handles the toggle event for the Sub-Agent Creation lock.
        """
        is_allowed = bool(state == Qt.Checked)
        if self.orchestrator_ref:
            self.orchestrator_ref.set_sub_agent_creation_lock(is_allowed)
        else:
            self.update_error_display("GUI", "Orchestrator reference not set. Cannot control sub-agent creation lock.")

    def update_agent_hierarchy_display(self, hierarchy: dict):
        """
        Updates the agent monitoring dashboard to display the hierarchy.
        This will require re-rendering or updating existing agent rows.
        For simplicity, we'll just print to chat for now, but a tree view would be ideal.
        """
        # Clear existing hierarchy display if any (for a full re-render approach)
        # For a simple update, iterate through existing widgets and update their labels
        
        # A more robust solution would involve a QTreeWidget or similar.
        # For now, let's update the agent's own row with its sub-agent count.
        for agent_name, widget_data in self.agent_widgets.items():
            sub_agent_count = self.orchestrator_ref.get_agent_team_count(agent_name) if self.orchestrator_ref else 0
            # Find the name label and update it to include the sub-agent count
            name_label = widget_data["name_label"]
            # Remove old count if it exists
            current_text = name_label.text()
            if " (Sub-Agents:" in current_text:
                current_text = current_text.split(" (Sub-Agents:")[0]
            
            name_label.setText(f"{current_text} (Sub-Agents: {sub_agent_count})")

        # Optionally, for debugging, print the hierarchy to the chat display
        hierarchy_str = "\nAgent Hierarchy:\n"
        if not hierarchy:
            hierarchy_str += "  (No sub-agents created yet)"
        else:
            for parent, children in hierarchy.items():
                hierarchy_str += f"  - {parent}:\n"
                for child in children:
                    hierarchy_str += f"    - {child}\n"
        
        # self.update_chat_display("GUI", hierarchy_str) # Uncomment for verbose hierarchy logging in chat

if __name__ == "__main__":
    app = QApplication(sys.argv)
    gui = AIAgentArmyGUI()
    # In a real application, the Orchestrator would be instantiated and passed here
    # For testing purposes, we can instantiate it here if needed for direct GUI interaction
    # from orchestrator import Orchestrator
    # orchestrator = Orchestrator(gui_ref=gui)
    # gui.orchestrator_ref = orchestrator
    gui.show()
    sys.exit(app.exec_())