#!/usr/bin/env python3
"""
Multi-Model Ensemble System inspired by AlphaEvolve
Uses multiple models for breadth and depth in algorithm discovery and validation
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed


class ModelRole(Enum):
    BREADTH = "breadth"  # Fast, diverse idea generation (like Gemini Flash)
    DEPTH = "depth"      # Deep, insightful analysis (like Gemini Pro)
    VALIDATION = "validation"  # Code validation and testing
    OPTIMIZATION = "optimization"  # Performance optimization


@dataclass
class ModelConfig:
    name: str
    role: ModelRole
    base_url: str
    model_id: str
    max_tokens: int
    temperature: float
    timeout: float
    weight: float  # Ensemble weight


@dataclass
class EnsembleResponse:
    responses: Dict[str, Any]
    consensus_score: float
    best_response: str
    confidence: float
    execution_time: float


class MultiModelEnsemble:
    """
    Multi-model ensemble system for enhanced algorithm discovery and validation
    Inspired by AlphaEvolve's use of multiple specialized models
    """
    
    def __init__(self, config_file: str = "ensemble_config.json"):
        self.config_file = config_file
        self.models = self._load_model_configs()
        self.logger = logging.getLogger(__name__)
        
        # Performance tracking
        self.model_performance = {}
        self.ensemble_history = []
        
        # Initialize model clients
        self._initialize_model_clients()
    
    def _load_model_configs(self) -> Dict[str, ModelConfig]:
        """Load model configurations"""
        default_configs = {
            "fast_generator": ModelConfig(
                name="fast_generator",
                role=ModelRole.BREADTH,
                base_url="http://localhost:11434",
                model_id="llama3.2:1b",  # Fast model for breadth
                max_tokens=2048,
                temperature=0.8,
                timeout=30.0,
                weight=0.3
            ),
            "deep_analyzer": ModelConfig(
                name="deep_analyzer", 
                role=ModelRole.DEPTH,
                base_url="http://localhost:11434",
                model_id="llama3.2:8b",  # Larger model for depth
                max_tokens=4096,
                temperature=0.3,
                timeout=120.0,
                weight=0.4
            ),
            "code_validator": ModelConfig(
                name="code_validator",
                role=ModelRole.VALIDATION,
                base_url="http://localhost:11434", 
                model_id="codellama:7b",  # Code-specialized model
                max_tokens=2048,
                temperature=0.1,
                timeout=60.0,
                weight=0.2
            ),
            "optimizer": ModelConfig(
                name="optimizer",
                role=ModelRole.OPTIMIZATION,
                base_url="http://localhost:11434",
                model_id="llama3.2:3b",  # Balanced model for optimization
                max_tokens=3072,
                temperature=0.2,
                timeout=90.0,
                weight=0.1
            )
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    loaded_configs = json.load(f)
                    
                # Convert to ModelConfig objects
                models = {}
                for name, config_data in loaded_configs.items():
                    models[name] = ModelConfig(**config_data)
                return models
        except Exception as e:
            self.logger.warning(f"Could not load model configs: {e}")
        
        return default_configs
    
    def _initialize_model_clients(self):
        """Initialize model clients for each configured model"""
        # Import enhanced connection handler
        from enhanced_connection_handler import EnhancedOllamaAPI
        
        self.model_clients = {}
        for name, config in self.models.items():
            try:
                client = EnhancedOllamaAPI(base_url=config.base_url)
                self.model_clients[name] = client
                self.model_performance[name] = {
                    "total_requests": 0,
                    "successful_requests": 0,
                    "average_response_time": 0.0,
                    "error_rate": 0.0
                }
                self.logger.info(f"Initialized model client: {name}")
            except Exception as e:
                self.logger.error(f"Failed to initialize model {name}: {e}")
    
    async def generate_algorithm_ensemble(self, problem_description: str, 
                                        algorithm_type: str,
                                        context: Dict[str, Any] = None) -> EnsembleResponse:
        """
        Generate algorithm solutions using ensemble of models
        Each model contributes based on its specialized role
        """
        if context is None:
            context = {}
        
        start_time = time.time()
        
        # Prepare role-specific prompts
        prompts = self._prepare_role_specific_prompts(problem_description, algorithm_type, context)
        
        # Execute models in parallel
        responses = await self._execute_parallel_inference(prompts)
        
        # Analyze and combine responses
        ensemble_result = self._analyze_ensemble_responses(responses)
        
        # Update performance tracking
        self._update_performance_tracking(responses, time.time() - start_time)
        
        return EnsembleResponse(
            responses=responses,
            consensus_score=ensemble_result["consensus_score"],
            best_response=ensemble_result["best_response"],
            confidence=ensemble_result["confidence"],
            execution_time=time.time() - start_time
        )
    
    def _prepare_role_specific_prompts(self, problem_description: str, 
                                     algorithm_type: str,
                                     context: Dict[str, Any]) -> Dict[str, str]:
        """Prepare specialized prompts for each model role"""
        
        base_context = f"""
Problem: {problem_description}
Algorithm Type: {algorithm_type}
Context: {json.dumps(context, indent=2)}
"""
        
        prompts = {}
        
        # Breadth model: Generate diverse solutions
        if "fast_generator" in self.models:
            prompts["fast_generator"] = f"""
{base_context}

You are a creative algorithm designer focused on generating diverse, innovative solutions.
Generate 3-5 different algorithmic approaches to solve this problem.
Focus on:
- Creative and unconventional approaches
- Different paradigms (iterative, recursive, heuristic, etc.)
- Quick, practical solutions
- Variety in implementation strategies

Provide Python code implementations for each approach.
"""
        
        # Depth model: Provide detailed analysis
        if "deep_analyzer" in self.models:
            prompts["deep_analyzer"] = f"""
{base_context}

You are an expert algorithm analyst focused on deep, thorough solutions.
Analyze this problem comprehensively and provide:
- Detailed algorithmic analysis
- Complexity considerations (time/space)
- Edge cases and error handling
- Optimization opportunities
- Mathematical foundations

Provide a single, well-optimized Python implementation with detailed comments.
"""
        
        # Validation model: Focus on correctness
        if "code_validator" in self.models:
            prompts["code_validator"] = f"""
{base_context}

You are a code validation specialist focused on correctness and robustness.
Create a solution that emphasizes:
- Input validation and error handling
- Type safety and bounds checking
- Comprehensive test cases
- Documentation and clarity
- Defensive programming practices

Provide Python code with extensive validation and testing.
"""
        
        # Optimization model: Focus on performance
        if "optimizer" in self.models:
            prompts["optimizer"] = f"""
{base_context}

You are a performance optimization expert focused on efficiency.
Create a solution optimized for:
- Minimal time complexity
- Efficient memory usage
- Scalability considerations
- Parallel processing opportunities
- Resource optimization

Provide highly optimized Python code with performance annotations.
"""
        
        return prompts
    
    async def _execute_parallel_inference(self, prompts: Dict[str, str]) -> Dict[str, Any]:
        """Execute inference on multiple models in parallel"""
        responses = {}
        
        # Create tasks for parallel execution
        tasks = []
        for model_name, prompt in prompts.items():
            if model_name in self.model_clients:
                task = asyncio.create_task(
                    self._single_model_inference(model_name, prompt)
                )
                tasks.append((model_name, task))
        
        # Wait for all tasks to complete
        for model_name, task in tasks:
            try:
                response = await task
                responses[model_name] = response
            except Exception as e:
                self.logger.error(f"Model {model_name} failed: {e}")
                responses[model_name] = {
                    "success": False,
                    "error": str(e),
                    "response": None,
                    "execution_time": 0.0
                }
        
        return responses
    
    async def _single_model_inference(self, model_name: str, prompt: str) -> Dict[str, Any]:
        """Execute inference on a single model"""
        start_time = time.time()
        
        try:
            config = self.models[model_name]
            client = self.model_clients[model_name]
            
            # Prepare messages
            messages = [{"role": "user", "content": prompt}]
            
            # Execute inference
            response = client.chat(
                model=config.model_id,
                messages=messages,
                stream=False,
                operation_timeout=config.timeout
            )
            
            if response and response.status_code == 200:
                response_data = response.json()
                content = response_data.get("message", {}).get("content", "")
                
                return {
                    "success": True,
                    "response": content,
                    "execution_time": time.time() - start_time,
                    "model_config": config,
                    "error": None
                }
            else:
                return {
                    "success": False,
                    "response": None,
                    "execution_time": time.time() - start_time,
                    "model_config": config,
                    "error": f"HTTP {response.status_code if response else 'No response'}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "response": None,
                "execution_time": time.time() - start_time,
                "model_config": self.models[model_name],
                "error": str(e)
            }
    
    def _analyze_ensemble_responses(self, responses: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze and combine ensemble responses"""
        successful_responses = {k: v for k, v in responses.items() if v.get("success", False)}
        
        if not successful_responses:
            return {
                "consensus_score": 0.0,
                "best_response": "",
                "confidence": 0.0
            }
        
        # Calculate consensus score based on response similarity
        consensus_score = self._calculate_consensus_score(successful_responses)
        
        # Select best response based on weighted scoring
        best_response_key, best_response = self._select_best_response(successful_responses)
        
        # Calculate confidence based on model performance and consensus
        confidence = self._calculate_confidence(successful_responses, consensus_score)
        
        return {
            "consensus_score": consensus_score,
            "best_response": best_response,
            "confidence": confidence,
            "best_model": best_response_key
        }
    
    def _calculate_consensus_score(self, responses: Dict[str, Any]) -> float:
        """Calculate consensus score among successful responses"""
        if len(responses) < 2:
            return 1.0
        
        # Simple consensus based on response length similarity
        # In practice, this would use more sophisticated similarity measures
        response_lengths = [len(r["response"]) for r in responses.values()]
        avg_length = sum(response_lengths) / len(response_lengths)
        
        # Calculate variance in response lengths
        variance = sum((length - avg_length) ** 2 for length in response_lengths) / len(response_lengths)
        
        # Convert variance to consensus score (lower variance = higher consensus)
        consensus_score = 1.0 / (1.0 + variance / (avg_length ** 2))
        
        return min(1.0, max(0.0, consensus_score))
    
    def _select_best_response(self, responses: Dict[str, Any]) -> Tuple[str, str]:
        """Select the best response based on weighted scoring"""
        best_score = -1.0
        best_key = ""
        best_response = ""
        
        for model_name, response_data in responses.items():
            if model_name not in self.models:
                continue
            
            config = self.models[model_name]
            performance = self.model_performance.get(model_name, {})
            
            # Calculate weighted score
            weight = config.weight
            success_rate = performance.get("successful_requests", 0) / max(1, performance.get("total_requests", 1))
            response_quality = len(response_data["response"]) / 1000.0  # Simple quality metric
            
            score = weight * 0.4 + success_rate * 0.3 + min(1.0, response_quality) * 0.3
            
            if score > best_score:
                best_score = score
                best_key = model_name
                best_response = response_data["response"]
        
        return best_key, best_response
    
    def _calculate_confidence(self, responses: Dict[str, Any], consensus_score: float) -> float:
        """Calculate overall confidence in the ensemble result"""
        # Factors affecting confidence:
        # 1. Number of successful responses
        # 2. Consensus score
        # 3. Historical model performance
        
        response_count_factor = min(1.0, len(responses) / len(self.models))
        consensus_factor = consensus_score
        
        # Average model performance
        avg_performance = 0.0
        for model_name in responses.keys():
            if model_name in self.model_performance:
                perf = self.model_performance[model_name]
                success_rate = perf.get("successful_requests", 0) / max(1, perf.get("total_requests", 1))
                avg_performance += success_rate
        
        if responses:
            avg_performance /= len(responses)
        
        # Combined confidence score
        confidence = (response_count_factor * 0.3 + 
                     consensus_factor * 0.4 + 
                     avg_performance * 0.3)
        
        return min(1.0, max(0.0, confidence))
    
    def _update_performance_tracking(self, responses: Dict[str, Any], total_time: float):
        """Update performance tracking for models"""
        for model_name, response_data in responses.items():
            if model_name not in self.model_performance:
                continue
            
            perf = self.model_performance[model_name]
            perf["total_requests"] += 1
            
            if response_data.get("success", False):
                perf["successful_requests"] += 1
            
            # Update average response time
            current_avg = perf["average_response_time"]
            new_time = response_data.get("execution_time", 0.0)
            total_requests = perf["total_requests"]
            
            perf["average_response_time"] = ((current_avg * (total_requests - 1)) + new_time) / total_requests
            
            # Update error rate
            perf["error_rate"] = 1.0 - (perf["successful_requests"] / perf["total_requests"])
    
    def get_model_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive model performance report"""
        report = {
            "models": self.model_performance.copy(),
            "ensemble_stats": {
                "total_ensemble_requests": len(self.ensemble_history),
                "average_consensus_score": 0.0,
                "average_confidence": 0.0
            }
        }
        
        if self.ensemble_history:
            report["ensemble_stats"]["average_consensus_score"] = sum(
                h.get("consensus_score", 0.0) for h in self.ensemble_history
            ) / len(self.ensemble_history)
            
            report["ensemble_stats"]["average_confidence"] = sum(
                h.get("confidence", 0.0) for h in self.ensemble_history
            ) / len(self.ensemble_history)
        
        return report
    
    def optimize_model_weights(self):
        """Optimize model weights based on performance history"""
        if not self.model_performance:
            return
        
        # Simple weight optimization based on success rates
        total_performance = 0.0
        for model_name, perf in self.model_performance.items():
            success_rate = perf.get("successful_requests", 0) / max(1, perf.get("total_requests", 1))
            response_time_factor = 1.0 / (1.0 + perf.get("average_response_time", 1.0))
            
            performance_score = success_rate * 0.7 + response_time_factor * 0.3
            total_performance += performance_score
        
        # Redistribute weights
        if total_performance > 0:
            for model_name, perf in self.model_performance.items():
                if model_name in self.models:
                    success_rate = perf.get("successful_requests", 0) / max(1, perf.get("total_requests", 1))
                    response_time_factor = 1.0 / (1.0 + perf.get("average_response_time", 1.0))
                    
                    performance_score = success_rate * 0.7 + response_time_factor * 0.3
                    new_weight = performance_score / total_performance
                    
                    self.models[model_name].weight = new_weight
        
        self.logger.info("Model weights optimized based on performance")
    
    def save_ensemble_config(self):
        """Save current ensemble configuration"""
        try:
            config_data = {}
            for name, config in self.models.items():
                config_data[name] = {
                    "name": config.name,
                    "role": config.role.value,
                    "base_url": config.base_url,
                    "model_id": config.model_id,
                    "max_tokens": config.max_tokens,
                    "temperature": config.temperature,
                    "timeout": config.timeout,
                    "weight": config.weight
                }
            
            with open(self.config_file, 'w') as f:
                json.dump(config_data, f, indent=2)
            
            self.logger.info(f"Ensemble configuration saved to {self.config_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save ensemble config: {e}")


# Async wrapper for easier integration
def run_ensemble_generation(problem_description: str, algorithm_type: str, 
                          context: Dict[str, Any] = None) -> EnsembleResponse:
    """Synchronous wrapper for ensemble generation"""
    ensemble = MultiModelEnsemble()
    
    async def _run():
        return await ensemble.generate_algorithm_ensemble(problem_description, algorithm_type, context)
    
    return asyncio.run(_run())
