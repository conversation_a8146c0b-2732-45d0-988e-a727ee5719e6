#!/usr/bin/env python3
"""
Test script for the Self-Upgrading Ollama Agent
Tests basic functionality without requiring <PERSON>llama to be running
"""

import sys
import json
from unittest.mock import Mock, patch
import requests

# Import the agent classes
from agent import OllamaAPI, SelfUpgradeSystem

def test_ollama_api():
    """Test OllamaAPI with mocked responses"""
    print("Testing OllamaAPI...")
    
    api = OllamaAPI()
    
    # Test with connection error (Ollama not running)
    try:
        models = api.list_models()
        print(f"Models (no Ollama): {models}")
        assert models == [], "Should return empty list when Ollama not running"
    except Exception as e:
        print(f"Expected error when Ollama not running: {e}")
    
    # Test with mocked successful response
    mock_response = Mock()
    mock_response.status_code = 200
    mock_response.json.return_value = {
        "models": [
            {"name": "llama2"},
            {"name": "codellama"}
        ]
    }
    
    with patch('requests.get', return_value=mock_response):
        models = api.list_models()
        print(f"Models (mocked): {models}")
        assert "llama2" in models, "Should contain llama2"
        assert "codellama" in models, "Should contain codellama"
    
    print("✓ OllamaAPI tests passed")

def test_self_upgrade_system():
    """Test SelfUpgradeSystem"""
    print("\nTesting SelfUpgradeSystem...")
    
    # Create a mock agent
    mock_agent = Mock()
    mock_agent.current_model = "llama2"
    mock_agent.ollama_api = Mock()
    
    upgrade_system = SelfUpgradeSystem(mock_agent)
    
    # Test update check
    update_info = upgrade_system.check_for_updates()
    print(f"Update info: {update_info}")
    assert "update_available" in update_info, "Should return update info"
    
    # Test code analysis
    analysis = upgrade_system.analyze_current_code()
    print(f"Code analysis: {analysis}")
    assert isinstance(analysis, str), "Should return string analysis"
    
    print("✓ SelfUpgradeSystem tests passed")

def test_imports():
    """Test that all required modules can be imported"""
    print("\nTesting imports...")
    
    try:
        import requests
        print("✓ requests imported")
    except ImportError as e:
        print(f"✗ requests import failed: {e}")
        return False
    
    try:
        from PyQt5.QtWidgets import QApplication
        print("✓ PyQt5.QtWidgets imported")
    except ImportError as e:
        print(f"✗ PyQt5.QtWidgets import failed: {e}")
        return False
    
    try:
        from PyQt5.QtCore import QThread, pyqtSignal, QTimer
        print("✓ PyQt5.QtCore imported")
    except ImportError as e:
        print(f"✗ PyQt5.QtCore import failed: {e}")
        return False
    
    try:
        from PyQt5.QtGui import QFont, QTextCursor
        print("✓ PyQt5.QtGui imported")
    except ImportError as e:
        print(f"✗ PyQt5.QtGui import failed: {e}")
        return False
    
    return True

def main():
    """Run all tests"""
    print("Running Self-Upgrading Ollama Agent Tests")
    print("=" * 50)
    
    # Test imports first
    if not test_imports():
        print("\n✗ Import tests failed - cannot continue")
        sys.exit(1)
    
    # Test core functionality
    try:
        test_ollama_api()
        test_self_upgrade_system()
        
        print("\n" + "=" * 50)
        print("✓ All tests passed!")
        print("\nThe agent should now work properly.")
        print("To run the GUI: python3 agent.py")
        
    except Exception as e:
        print(f"\n✗ Tests failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
