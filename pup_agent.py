import time
import random
from agent_base import AgentBase

class PupAgent(AgentBase):
    """
    A sub-agent created by a Hound agent, with its lifecycle tied to its parent Hound.
    """
    def __init__(self, name: str, orchestrator, hound_ref, is_sub_agent: bool = True):
        super().__init__(name=name, orchestrator=orchestrator, is_sub_agent=is_sub_agent)
        self.hound_ref = hound_ref
        # PupAgents utilize the Context7MCPServer for context management
        if not self.context_7_mcp_server: # Ensure it's initialized if not by AgentBase
            self.context_7_mcp_server = Context7MCPServer()
        print(f"PupAgent {self.name} initialized with Context7MCPServer for context.")

    def follow_parent_orders(self, order: dict):
        """
        Simulates executing orders from its parent Hound agent with security checks.
        Ensures orders from the parent HoundAgent are valid.
        """
        print(f"Pup Agent {self.name} received order from parent Hound: {order.get('description')}")
        self.set_status("executing_order")

        # Simulated security check: Validate parent Hound agent
        if not self.validate_parent_hound():
            print(f"Pup Agent {self.name} SECURITY ALERT - Parent Hound validation failed")
            self.error_logger.log(f"PupAgent: Parent Hound validation failed")
            # Notify SentinelAgent of potential issue
            self.nexus_light.send_message("SentinelAgent", {
                "type": "security_alert",
                "details": f"PupAgent {self.name} parent Hound validation failed"
            })
            self.set_status("idle")
            return

        # Simulated security check: Validate order content
        if not self.validate_order_content(order):
            print(f"Pup Agent {self.name} SECURITY ALERT - Invalid order received: {order}")
            self.error_logger.log(f"PupAgent: Invalid order received from parent Hound")
            # Notify SentinelAgent of potential issue
            self.nexus_light.send_message("SentinelAgent", {
                "type": "security_alert",
                "details": f"PupAgent {self.name} received invalid order: {order}"
            })
            self.set_status("idle")
            return

        # Placeholder for actual order execution logic
        time.sleep(random.uniform(1, 2))
        print(f"Pup Agent {self.name} completed order: {order.get('description')}")

        # Simulate context interaction
        self.context_7_mcp_server.update_context(self.name, f"Executed parent order: {order.get('description')}")
        retrieved_context = self.context_7_mcp_server.get_context(self.name, "parent order execution")
        print(f"Pup Agent {self.name} context after order: {retrieved_context}")
        self.set_status("idle")

    def validate_parent_hound(self) -> bool:
        """
        Simulates validating the parent Hound agent to ensure it hasn't been compromised.
        Returns True if the parent is valid, False otherwise.
        """
        print(f"Pup Agent {self.name} validating parent Hound: {self.hound_ref.name}")

        # Simulated validation logic
        # In a real scenario, this would involve checking the parent's status and integrity
        if not self.hound_ref or self.hound_ref._is_killed:
            print(f"Pup Agent {self.name} detected parent Hound is invalid or killed")
            return False

        # Simulate occasional validation failure for demonstration
        if random.random() > 0.9:
            print(f"Pup Agent {self.name} detected potential compromise in parent Hound")
            return False

        print(f"Pup Agent {self.name} confirmed parent Hound is valid")
        return True

    def validate_order_content(self, order: dict) -> bool:
        """
        Simulates validating the content of an order from the parent Hound.
        Returns True if the content is valid, False otherwise.
        """
        print(f"Pup Agent {self.name} validating order content: {order}")

        # Simulated validation logic
        # Check for required fields and basic sanity checks
        required_fields = ['description', 'target']
        if not all(field in order for field in required_fields):
            print(f"Pup Agent {self.name} detected missing fields in order")
            return False

        print(f"Pup Agent {self.name} confirmed valid order content")
        return True

    def neutralize_parent_hound(self):
        """
        Simulates the emergency protocol for neutralizing a compromised parent HoundAgent.
        This involves simulated actions like sending a critical message to Sentinel and
        attempting to stop the Hound's process, with additional security checks.
        """
        print(f"Pup Agent {self.name} activating emergency protocol to neutralize compromised parent Hound: {self.hound_ref.name}")
        self.set_status("neutralizing_parent")

        # Simulated security check: Confirm parent Hound is actually compromised
        if not self.confirm_parent_compromised():
            print(f"Pup Agent {self.name} SECURITY ALERT - Parent Hound compromise not confirmed")
            self.error_logger.log(f"PupAgent: Parent Hound compromise not confirmed")
            # Notify SentinelAgent of potential issue
            self.nexus_light.send_message("SentinelAgent", {
                "type": "security_alert",
                "details": f"PupAgent {self.name} parent Hound compromise not confirmed"
            })
            self.set_status("idle")
            return

        # Simulate sending a critical message to Sentinel
        critical_message = {
            "type": "critical_alert",
            "sender": self.name,
            "target_agent": self.hound_ref.name,
            "reason": "Parent Hound compromised/critical error",
            "action_taken": "Attempting neutralization"
        }
        print(f"SIMULATING: Sending critical alert to Sentinel: {critical_message}")
        self.nexus_light.send_message(
            recipient_agent_name="SentinelAgent", # Assuming SentinelAgent is the name
            message_payload=critical_message
        )

        # Simulate attempting to stop the Hound's process
        print(f"SIMULATING: Attempting to stop parent Hound's process: {self.hound_ref.name}")
        self.hound_ref.kill() # Call the kill method on the parent Hound reference
        time.sleep(random.uniform(1, 3))
        print(f"SIMULATED: Parent Hound {self.hound_ref.name} neutralization attempt complete.")
        self.set_status("idle")

    def confirm_parent_compromised(self) -> bool:
        """
        Simulates confirming that the parent Hound agent is actually compromised.
        Returns True if compromise is confirmed, False otherwise.
        """
        print(f"Pup Agent {self.name} confirming parent Hound {self.hound_ref.name} is compromised...")

        # Simulated confirmation logic
        # In a real scenario, this would involve checking logs, behavior patterns, etc.
        if self.hound_ref._is_killed:
            print(f"Pup Agent {self.name} confirmed parent Hound {self.hound_ref.name} is killed")
            return True

        # Simulate occasional confirmation of compromise
        if random.random() > 0.7:
            print(f"Pup Agent {self.name} detected signs of compromise in parent Hound {self.hound_ref.name}")
            return True

        print(f"Pup Agent {self.name} could not confirm parent Hound {self.hound_ref.name} is compromised")
        return False

    def run(self):
        """
        Main execution loop for the Pup Agent.
        Includes a mechanism to simulate its termination if its parent HoundAgent is killed.
        """
        self.set_status("running")
        print(f"Pup Agent {self.name} started, tied to parent Hound: {self.hound_ref.name}")

        while self.is_active and not self._is_killed:
            # Check if parent Hound is killed
            if self.hound_ref._is_killed:
                print(f"Pup Agent {self.name}: Parent Hound {self.hound_ref.name} has been killed. Terminating self.")
                self.kill() # Terminate self if parent is killed
                break # Exit the loop

            try:
                self.set_status("idle")
                print(f"Pup Agent {self.name} is idle, waiting for orders from parent Hound or Orchestrator...")
                
                # Simulate receiving orders or tasks
                response = self.nexus_light.query_orchestrator_for_directives()
                
                if response and response.get("type") == "task_assigned":
                    task = response.get("task")
                    self.current_task = task
                    self.follow_parent_orders(task) # Pups follow parent orders
                    
                    # Report task completion
                    self.nexus_light.send_message(
                        recipient_agent_name="Orchestrator",
                        message_payload={
                            "type": "task_completed",
                            "task_id": task["task_id"],
                            "status": "completed",
                            "content": f"Task '{task['description']}' completed by Pup Agent {self.name}."
                        }
                    )
                    self.set_status("completed_task")
                    self.current_task = None
                elif response and response.get("type") == "no_task_available":
                    print(f"Pup Agent {self.name}: {response.get('content')}")
                    self.set_status("idle")
                    time.sleep(5)
                else:
                    print(f"Pup Agent {self.name}: No valid response from Orchestrator or no task assigned. Waiting...")
                    self.set_status("idle")
                    time.sleep(5)

                self.report_status_to_gui()
            except Exception as e:
                error_message = f"Unhandled exception in Pup Agent {self.name}: {e}"
                details = {"exception_type": type(e).__name__, "traceback": str(e)}
                self.error_logger.log_error(
                    agent_name=self.name,
                    error_message=error_message,
                    details=details
                )
                self.set_status("error")
                time.sleep(5)
        
        if self._is_killed:
            self.set_status("killed")
            print(f"Pup Agent {self.name} terminated.")
        elif not self.is_active:
            self.set_status("stopped")
            print(f"Pup Agent {self.name} gracefully stopped/paused.")