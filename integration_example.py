#!/usr/bin/env python3
"""
Integration Example: How to integrate all enhancements into the existing agent
"""

import sys
import os
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTabWidget
from PyQt5.QtCore import QTimer

# Import enhanced components
from enhanced_config_manager import EnhancedConfigManager
from enhanced_connection_handler import EnhancedOllamaAPI
from enhanced_error_handling import <PERSON>hanced<PERSON><PERSON>r<PERSON><PERSON><PERSON>, ErrorCategory, ErrorSeverity, handle_error
from auto_loop_system import AutoLoopSystem, AutoLoopControlWidget, OperationMode
from improved_self_upgrade_system import EnhancedSelfUpgradeSystem


class EnhancedOllamaAgentGUI(QMainWindow):
    """Enhanced version of the Ollama Agent with all improvements integrated"""
    
    def __init__(self):
        super().__init__()
        
        # Initialize enhanced components
        self.config_manager = EnhancedConfigManager()
        self.error_handler = EnhancedErrorHandler()
        self.setup_logging()
        
        # Initialize enhanced API with configuration
        self.ollama_api = EnhancedOllamaAPI(
            base_url=self.config_manager.ollama.base_url,
            config_file="connection_config.json"
        )
        
        # Initialize auto-loop system
        self.auto_loop = AutoLoopSystem()
        
        # Initialize enhanced upgrade system
        self.upgrade_system = EnhancedSelfUpgradeSystem(self)
        
        # Application state
        self.current_model = "llama2"
        self.chat_history = []
        self.chat_thread = None
        
        # Initialize UI
        self.init_enhanced_ui()
        
        # Setup auto-save timer
        self.setup_auto_save()
        
        # Load models asynchronously
        QTimer.singleShot(100, self.load_models_with_error_handling)
    
    def setup_logging(self):
        """Setup enhanced logging based on configuration"""
        log_config = self.config_manager.logging
        
        # Configure root logger
        logging.basicConfig(
            level=getattr(logging, log_config.level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        if log_config.file_enabled:
            # Add file handler
            file_handler = logging.FileHandler(log_config.file_path)
            file_handler.setLevel(getattr(logging, log_config.level))
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(formatter)
            logging.getLogger().addHandler(file_handler)
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("Enhanced Ollama Agent starting up...")
    
    def init_enhanced_ui(self):
        """Initialize enhanced UI with all improvements"""
        # Apply configuration
        ui_config = self.config_manager.ui
        self.setWindowTitle("Enhanced Self-Upgrading Ollama Agent")
        self.setGeometry(ui_config.window_x, ui_config.window_y, 
                        ui_config.window_width, ui_config.window_height)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # Create enhanced tab widget
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # Create tabs with enhanced functionality
        self.create_enhanced_chat_tab()
        self.create_enhanced_model_tab()
        self.create_enhanced_upgrade_tab()
        self.create_auto_loop_tab()
        self.create_enhanced_settings_tab()
        self.create_monitoring_tab()
    
    def create_enhanced_chat_tab(self):
        """Create enhanced chat tab with error handling"""
        from PyQt5.QtWidgets import QTextEdit, QLineEdit, QPushButton, QHBoxLayout, QComboBox, QLabel
        
        chat_widget = QWidget()
        layout = QVBoxLayout(chat_widget)
        
        # Model selection with error handling
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("Model:"))
        self.model_combo = QComboBox()
        self.model_combo.currentTextChanged.connect(self.on_model_changed_with_error_handling)
        model_layout.addWidget(self.model_combo)
        layout.addLayout(model_layout)
        
        # Chat display
        self.chat_display = QTextEdit()
        self.chat_display.setReadOnly(True)
        layout.addWidget(self.chat_display)
        
        # Input area
        input_layout = QHBoxLayout()
        self.chat_input = QLineEdit()
        self.chat_input.returnPressed.connect(self.send_message_with_auto_loop)
        input_layout.addWidget(self.chat_input)
        
        self.send_button = QPushButton("Send")
        self.send_button.clicked.connect(self.send_message_with_auto_loop)
        input_layout.addWidget(self.send_button)
        
        layout.addLayout(input_layout)
        self.tab_widget.addTab(chat_widget, "Enhanced Chat")
    
    def create_auto_loop_tab(self):
        """Create auto-loop control tab"""
        self.auto_loop_widget = AutoLoopControlWidget(self.auto_loop)
        self.tab_widget.addTab(self.auto_loop_widget, "Auto-Loop Control")
    
    def create_enhanced_upgrade_tab(self):
        """Create enhanced upgrade tab with improved validation"""
        from PyQt5.QtWidgets import QTextEdit, QPushButton, QLabel, QGroupBox
        
        upgrade_widget = QWidget()
        layout = QVBoxLayout(upgrade_widget)
        
        # Upgrade control group
        control_group = QGroupBox("Enhanced Upgrade Control")
        control_layout = QVBoxLayout(control_group)
        
        # Version info
        self.version_label = QLabel(f"Current Version: {self.upgrade_system.current_version}")
        control_layout.addWidget(self.version_label)
        
        # Buttons with enhanced functionality
        button_layout = QHBoxLayout()
        
        check_btn = QPushButton("Check for Updates")
        check_btn.clicked.connect(self.check_updates_with_error_handling)
        button_layout.addWidget(check_btn)
        
        validate_btn = QPushButton("Enhanced Validation")
        validate_btn.clicked.connect(self.enhanced_code_validation)
        button_layout.addWidget(validate_btn)
        
        backup_btn = QPushButton("Create Backup")
        backup_btn.clicked.connect(self.create_backup_with_error_handling)
        button_layout.addWidget(backup_btn)
        
        control_layout.addLayout(button_layout)
        layout.addWidget(control_group)
        
        # Code display
        self.upgrade_code_display = QTextEdit()
        layout.addWidget(self.upgrade_code_display)
        
        self.tab_widget.addTab(upgrade_widget, "Enhanced Upgrade")
    
    def create_enhanced_model_tab(self):
        """Create enhanced model management tab"""
        from PyQt5.QtWidgets import QTextEdit, QPushButton, QLineEdit, QProgressBar
        
        model_widget = QWidget()
        layout = QVBoxLayout(model_widget)
        
        # Model list with enhanced info
        self.model_list = QTextEdit()
        self.model_list.setMaximumHeight(200)
        layout.addWidget(self.model_list)
        
        # Enhanced model operations
        ops_layout = QHBoxLayout()
        
        refresh_btn = QPushButton("Refresh Models")
        refresh_btn.clicked.connect(self.load_models_with_error_handling)
        ops_layout.addWidget(refresh_btn)
        
        self.pull_model_input = QLineEdit()
        self.pull_model_input.setPlaceholderText("Model name (e.g., llama3.2:1b)")
        ops_layout.addWidget(self.pull_model_input)
        
        pull_btn = QPushButton("Pull Model")
        pull_btn.clicked.connect(self.pull_model_with_progress)
        ops_layout.addWidget(pull_btn)
        
        layout.addLayout(ops_layout)
        
        # Progress bar for model operations
        self.model_progress = QProgressBar()
        self.model_progress.setVisible(False)
        layout.addWidget(self.model_progress)
        
        self.tab_widget.addTab(model_widget, "Enhanced Models")
    
    def create_enhanced_settings_tab(self):
        """Create enhanced settings tab with configuration management"""
        from PyQt5.QtWidgets import QGroupBox, QLineEdit, QSpinBox, QCheckBox, QComboBox
        
        settings_widget = QWidget()
        layout = QVBoxLayout(settings_widget)
        
        # Connection settings
        conn_group = QGroupBox("Connection Settings")
        conn_layout = QVBoxLayout(conn_group)
        
        # Ollama URL
        url_layout = QHBoxLayout()
        url_layout.addWidget(QLabel("Ollama URL:"))
        self.ollama_url_input = QLineEdit(self.config_manager.ollama.base_url)
        self.ollama_url_input.textChanged.connect(self.on_config_changed)
        url_layout.addWidget(self.ollama_url_input)
        conn_layout.addLayout(url_layout)
        
        # Timeout settings
        timeout_layout = QHBoxLayout()
        timeout_layout.addWidget(QLabel("Chat Timeout (seconds):"))
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(30, 600)
        self.timeout_spin.setValue(int(self.config_manager.ollama.timeout_chat))
        self.timeout_spin.valueChanged.connect(self.on_config_changed)
        timeout_layout.addWidget(self.timeout_spin)
        conn_layout.addLayout(timeout_layout)
        
        layout.addWidget(conn_group)
        
        # Auto-loop settings
        auto_group = QGroupBox("Auto-Loop Settings")
        auto_layout = QVBoxLayout(auto_group)
        
        self.auto_loop_enabled_cb = QCheckBox("Enable Auto-Loop")
        self.auto_loop_enabled_cb.setChecked(self.config_manager.auto_loop.enabled)
        self.auto_loop_enabled_cb.toggled.connect(self.on_auto_loop_toggled)
        auto_layout.addWidget(self.auto_loop_enabled_cb)
        
        layout.addWidget(auto_group)
        
        # Configuration management
        config_group = QGroupBox("Configuration Management")
        config_layout = QVBoxLayout(config_group)
        
        config_btn_layout = QHBoxLayout()
        
        save_config_btn = QPushButton("Save Configuration")
        save_config_btn.clicked.connect(self.save_configuration)
        config_btn_layout.addWidget(save_config_btn)
        
        reset_config_btn = QPushButton("Reset to Defaults")
        reset_config_btn.clicked.connect(self.reset_configuration)
        config_btn_layout.addWidget(reset_config_btn)
        
        export_config_btn = QPushButton("Export Config")
        export_config_btn.clicked.connect(self.export_configuration)
        config_btn_layout.addWidget(export_config_btn)
        
        config_layout.addLayout(config_btn_layout)
        layout.addWidget(config_group)
        
        self.tab_widget.addTab(settings_widget, "Enhanced Settings")
    
    def create_monitoring_tab(self):
        """Create monitoring and diagnostics tab"""
        from PyQt5.QtWidgets import QTextEdit, QPushButton
        
        monitor_widget = QWidget()
        layout = QVBoxLayout(monitor_widget)
        
        # Error statistics
        self.error_stats_display = QTextEdit()
        self.error_stats_display.setMaximumHeight(150)
        layout.addWidget(self.error_stats_display)
        
        # Connection status
        self.connection_status_display = QTextEdit()
        self.connection_status_display.setMaximumHeight(100)
        layout.addWidget(self.connection_status_display)
        
        # Control buttons
        btn_layout = QHBoxLayout()
        
        refresh_stats_btn = QPushButton("Refresh Statistics")
        refresh_stats_btn.clicked.connect(self.refresh_monitoring_data)
        btn_layout.addWidget(refresh_stats_btn)
        
        clear_errors_btn = QPushButton("Clear Error History")
        clear_errors_btn.clicked.connect(self.clear_error_history)
        btn_layout.addWidget(clear_errors_btn)
        
        export_report_btn = QPushButton("Export Error Report")
        export_report_btn.clicked.connect(self.export_error_report)
        btn_layout.addWidget(export_report_btn)
        
        layout.addLayout(btn_layout)
        self.tab_widget.addTab(monitor_widget, "Monitoring")
        
        # Setup monitoring timer
        self.monitoring_timer = QTimer()
        self.monitoring_timer.timeout.connect(self.refresh_monitoring_data)
        self.monitoring_timer.start(5000)  # Update every 5 seconds
    
    # Enhanced methods with error handling and auto-loop integration
    
    def load_models_with_error_handling(self):
        """Load models with enhanced error handling"""
        try:
            models = self.ollama_api.list_models()
            self.model_combo.clear()
            self.model_combo.addItems(models)
            
            if models:
                model_text = f"Found {len(models)} models:\n"
                for model in models:
                    model_text += f"• {model}\n"
                self.model_list.setText(model_text)
                self.logger.info(f"Successfully loaded {len(models)} models")
            else:
                self.model_list.setText("No models available. Check Ollama connection.")
                
        except Exception as e:
            handle_error(e, ErrorCategory.MODEL, ErrorSeverity.MEDIUM, 
                        {"operation": "load_models"})
            self.model_list.setText(f"Error loading models: {str(e)}")
    
    def send_message_with_auto_loop(self):
        """Send message with auto-loop integration"""
        message = self.chat_input.text().strip()
        if not message:
            return
        
        # Add to auto-loop system for retry capability
        operation_id = f"chat_{len(self.chat_history)}"
        self.auto_loop.add_operation(
            operation_id,
            f"Chat: {message[:30]}...",
            self._send_chat_message,
            message
        )
        
        self.chat_input.clear()
    
    def _send_chat_message(self, message: str) -> bool:
        """Internal method for sending chat messages"""
        try:
            # Add user message to display
            self.chat_display.append(f"You: {message}\n")
            
            # Send to Ollama
            response = self.ollama_api.chat(
                self.current_model,
                [{"role": "user", "content": message}],
                stream=False
            )
            
            if response and response.status_code == 200:
                response_data = response.json()
                if 'message' in response_data:
                    ai_response = response_data['message']['content']
                    self.chat_display.append(f"AI: {ai_response}\n\n")
                    return True
            
            return False
            
        except Exception as e:
            handle_error(e, ErrorCategory.CONNECTION, ErrorSeverity.MEDIUM,
                        {"operation": "send_chat_message", "message": message})
            return False
    
    def setup_auto_save(self):
        """Setup automatic configuration saving"""
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self.auto_save_config)
        self.auto_save_timer.start(self.config_manager.ui.auto_save_interval * 1000)
    
    def auto_save_config(self):
        """Automatically save configuration"""
        try:
            self.config_manager.save_all_configs()
            self.logger.debug("Configuration auto-saved")
        except Exception as e:
            handle_error(e, ErrorCategory.SYSTEM, ErrorSeverity.LOW,
                        {"operation": "auto_save_config"})
    
    # Additional enhanced methods would be implemented here...
    
    def closeEvent(self, event):
        """Enhanced cleanup on application close"""
        try:
            # Stop auto-loop system
            self.auto_loop.stop_auto_loop()
            
            # Save configuration
            self.config_manager.save_all_configs()
            
            # Close connections
            self.ollama_api.close()
            
            # Stop timers
            if hasattr(self, 'monitoring_timer'):
                self.monitoring_timer.stop()
            if hasattr(self, 'auto_save_timer'):
                self.auto_save_timer.stop()
            
            self.logger.info("Enhanced Ollama Agent shutting down...")
            event.accept()
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
            event.accept()


def main():
    """Enhanced main function with comprehensive error handling"""
    try:
        app = QApplication(sys.argv)
        app.setStyle('Fusion')
        
        # Create and show enhanced window
        window = EnhancedOllamaAgentGUI()
        window.show()
        
        sys.exit(app.exec_())
        
    except Exception as e:
        logging.critical(f"Critical error in main: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
