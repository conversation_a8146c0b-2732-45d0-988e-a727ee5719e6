# Plan to Update Memory Bank Files

**Goal:** Update `memory-bank/generalFeatures.md`, `memory-bank/systemPatterns.md`, and `memory-bank/agents.md` to reflect the new sub-agent context management rule, specifically regarding RAG databases and Context 7 MCP server usage.

**Mermaid Diagram: Agent Context Management Flow**

```mermaid
graph TD
    A[Main Agents] -- Has dedicated --> B(RAG Database)
    C[Sub-Agents] -- Relies on --> D(Custom Context 7 MCP Server)
    E[All Agents] -- Utilize --> F(Custom System Prompt Structure)
    F -- Integrates --> G(Custom Cline Memory Bank)
    F -- Integrates --> H(Custom Taskmaster MCP Server)
    F -- Integrates --> I(Custom Context 7 MCP Servers)
```

**Detailed Steps:**

1.  **Update `memory-bank/generalFeatures.md`**
    *   **Action 1.1:** Modify the "RAG Databases" section to clearly state the distinction: "All main agents... must possess and utilize their own dedicated RAG database."
    *   **Action 1.2:** Add a new section or modify an existing one to state: "Sub-Agent Context Management: To save resources, sub-agents will NOT have individual RAG databases. Instead, they will rely on a custom version of Context 7 MCP server for their context and memory management." This will be inserted after the existing "RAG Databases" bullet point.
    *   **Action 1.3:** Ensure the "System Prompts & Context" section accurately reflects that *all* agents (main and sub-agents) use the combined system prompt structure. This will involve a minor rephrasing to emphasize "all agents."

2.  **Update `memory-bank/systemPatterns.md`**
    *   **Action 2.1:** Add a new section or update existing architecture descriptions to reflect this dual approach to context management (RAG for main agents, Context 7 MCP for sub-agents). I will add a new subsection under "5. General Agent Features & Rules (Design Patterns & Mechanisms)" to describe this.
    *   **Action 2.2:** Mention the "custom version of Context 7 MCP server" as a key technical decision for resource optimization within the new or updated section.

3.  **Update `memory-bank/agents.md`**
    *   **Action 3.1:** For each primary agent (Orchestrator, Search Agent, Programming Agent, Debugging/Optimization Agent, Communications Agent, and agents in the Self-Upgrading Agent Contingent), explicitly mention that it has its "own dedicated RAG database." I will review each agent's description under "Responsibilities" or "Capabilities" and add this detail.
    *   **Action 3.2:** For Hound Agents, clarify that they "do not have individual RAG databases" and instead use the "custom version of Context 7 MCP server" for context management. This will be added under the "Hound Agents & Pups" section.
    *   **Action 3.3:** For Pups, explicitly state that they "will utilize a custom version of Context 7 MCP server for their context and memory management." This will also be added under the "Hound Agents & Pups" section.
