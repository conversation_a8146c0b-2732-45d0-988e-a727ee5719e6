# orchestrator.py

from agent_base import Agent<PERSON><PERSON>
from error_logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from nexus_light import <PERSON>exus<PERSON><PERSON>
from rag_database import RAGDatabase # Import RAGDatabase
import random
import uuid # Import uuid for generating unique task IDs
import time # Import time for simulating uptime
from hardware_monitor import HardwareMonitor # Import HardwareMonitor
from PyQt5.QtCore import QTimer, QObject, pyqtSignal # Import QTimer for periodic updates

# Import specific agent types
from search_agent import SearchAgent
from programming_agent import ProgrammingAgent
from debugging_optimization_agent import DebuggingOptimizationAgent
from communications_agent import CommunicationsAgent
from self_upgrading_agent import SelfUpgradingAgent
from lovelo_agent import LoveloAgent
from upload_agent import UploadAgent
from sentinel_agent import SentinelAgent
from hound_agent import HoundAgent
from pup_agent import PupAgent
from reconnaissance_agent import ReconnaissanceAgent

class Orchestrator: # Orchestrator no longer inherits from AgentBase directly
    """
    The central control unit for all other agents.
    """
    def __init__(self, gui_ref=None):
        self.managed_agents = [] # List to hold agent instances
        self.active_agents_count = 0 # Track total active agents
        self.active_search_agents_count = 0 # Track active Search Agents
        self.agent_name_counter = 0 # Mechanism for dynamic agent naming
        self.projects = {} # Placeholder for project management
        self.gui_ref = gui_ref # Reference to the GUI for updates
        self.tasks = {} # Dictionary to manage tasks: {task_id: {description, assigned_to, status, progress}}
        self.agent_hierarchy = {} # To track parent-child relationships: {"parent_agent_name": ["child1", "child2"]}
        self.sub_agent_creation_allowed = False # Default to False for sub-agent creation lock
        self.main_agents = ["SearchAgent", "ProgrammingAgent", "DebuggingOptimizationAgent", "CommunicationsAgent", "SelfUpgradingAgent", "SentinelAgent"] # Main agents allowed to create sub-agents even when locked

        # Simulated settings storage
        self.api_keys = ["sim_key_123", "sim_key_456"]
        self.llm_selection = {"primary": "Gemini-Pro", "sub_agent_default": "Inherit from Parent"}
        self.theme_settings = {"accent_color": "#007acc", "dark_mode_intensity": 80}
        self.persistence_settings = {"auto_save": True, "backups": False}
        self.log_config = {"verbosity": "Info", "retention_days": 30}
        self.notification_prefs = {"on_errors": True, "on_status_changes": False}

        self.start_time = time.time() # For system uptime simulation
        self.system_metrics_timer = QTimer() # Timer for periodic system metric updates
        self.system_metrics_timer.setInterval(1000) # Update every 1 second (1000 ms)
        self.system_metrics_timer.timeout.connect(self._periodic_system_metrics_update)
        self.system_metrics_timer.start()

        # self.sandbox_git_timer = QTimer() # Timer for periodic sandbox/git updates - Removed as per user feedback
        # self.sandbox_git_timer.setInterval(3000) # Update every 3 seconds
        # self.sandbox_git_timer.timeout.connect(self._periodic_sandbox_git_update)
        # self.sandbox_git_timer.start()

        # Initialize HardwareMonitor
        self.hardware_monitor = HardwareMonitor()
        self.hardware_monitor.set_orchestrator_callback(self.handle_new_device_detection)

        # Initialize RAG database and system prompt for the Orchestrator
        self.rag_database = RAGDatabase()
        self.system_prompt = (
            "You are the Orchestrator, the central control unit of the AI Agent Army. "
            "Your primary role is to manage agents, assign tasks, handle errors, "
            "and ensure the overall system operates efficiently and securely."
        )

        # Initialize ErrorLogger and NexusLight for the Orchestrator
        self.error_logger = ErrorLogger(orchestrator_ref=self)
        self.nexus_light = NexusLight(agent_name="Orchestrator", orchestrator_ref=self)
        print("Orchestrator initialized.")

        # --- Phase 4: Initialize Self-Upgrading Agent Contingent ---
        print("Orchestrator: Initializing Self-Upgrading Agent Contingent...")
        # Create 5 instances of the base SelfUpgradingAgent
        for i in range(5):
            self.create_agent("SelfUpgradingAgent", parent_agent_name="Orchestrator") # Orchestrator is their direct manager
        
        # Create the specialized Lovelo Agent
        self.create_agent("LoveloAgent", parent_agent_name="Orchestrator")

        # Create the specialized Upload Agent
        self.create_agent("UploadAgent", parent_agent_name="Orchestrator")
        print("Orchestrator: Self-Upgrading Agent Contingent initialized.")
        # -----------------------------------------------------------

    def assign_task(self, agent_name: str, task_description: str, initial_status: str = "pending", initial_progress: int = 0):
        """
        Assigns a task to a specific agent and adds it to the task management system.
        """
        task_id = str(uuid.uuid4())
        self.tasks[task_id] = {
            "task_id": task_id, # Store task_id explicitly for GUI
            "description": task_description,
            "assigned_to": agent_name,
            "status": initial_status,
            "progress": initial_progress # Add progress
        }
        print(f"Orchestrator: Assigned task '{task_description}' (ID: {task_id}) to agent {agent_name}. Status: {initial_status}, Progress: {initial_progress}%")
        if self.gui_ref:
            self.gui_ref.update_chat_display("Orchestrator", f"Assigned task '{task_description}' (ID: {task_id}) to {agent_name}.")
            self.gui_ref.update_task_board(self.get_all_tasks_for_gui()) # Update GUI task board
        return task_id

    def get_available_task(self, agent_name: str):
        """
        Iterates through pending tasks and assigns an unassigned one to the requesting agent.
        """
        for task_id, task_info in self.tasks.items():
            if task_info["status"] == "pending" and task_info["assigned_to"] is None:
                task_info["assigned_to"] = agent_name
                task_info["status"] = "in_progress"
                print(f"Orchestrator: Assigned available task '{task_info['description']}' (ID: {task_id}) to {agent_name}.")
                if self.gui_ref:
                    self.gui_ref.update_chat_display("Orchestrator", f"Assigned available task '{task_info['description']}' (ID: {task_id}) to {agent_name}.")
                return {"task_id": task_id, "description": task_info["description"]}
        print(f"Orchestrator: No available tasks for {agent_name}.")
        return None

    def update_task_status(self, task_id: str, status: str, agent_name: str, progress: int = None):
        """
        Allows agents to report task completion or issues, updating the task status and progress.
        """
        if task_id in self.tasks:
            if self.tasks[task_id]["assigned_to"] == agent_name:
                self.tasks[task_id]["status"] = status
                if progress is not None:
                    self.tasks[task_id]["progress"] = progress
                print(f"Orchestrator: Task '{task_id}' status updated to '{status}' by {agent_name}. Progress: {self.tasks[task_id]['progress']}%")
                if self.gui_ref:
                    self.gui_ref.update_chat_display("Orchestrator", f"Task '{task_id}' status updated to '{status}' by {agent_name}.")
                    self.gui_ref.update_task_board(self.get_all_tasks_for_gui()) # Update GUI task board
            else:
                print(f"Orchestrator Error: Agent {agent_name} attempted to update task {task_id} not assigned to it.")
                self.error_logger.log_error("Orchestrator", f"Unauthorized task update attempt by {agent_name} for task {task_id}")
        else:
            print(f"Orchestrator Error: Task ID {task_id} not found.")
            self.error_logger.log_error("Orchestrator", f"Task ID not found: {task_id}")

    def manage_projects(self):
        """
        Placeholder method for managing multiple projects concurrently.
        """
        print("Orchestrator: Managing projects...")
        # Logic for project management, task distribution, etc.

    def access_kill_switch(self, agent_name: str):
        """
        Placeholder method to access the kill switch of a specific agent.
        """
        for agent in self.managed_agents:
            if agent.name == agent_name:
                agent.stop() # Call the new stop() method
                print(f"Orchestrator: Accessed kill switch for {agent_name}. Agent paused.")
                if self.gui_ref:
                    self.gui_ref.update_chat_display("Orchestrator", f"Kill switch activated for {agent_name}. Agent paused.")
                    self.gui_ref.update_kill_switch_status(agent_name, False) # Update GUI status
                return
        print(f"Orchestrator: Agent {agent_name} not found for kill switch access.")
        if self.gui_ref:
            self.gui_ref.update_error_display("Orchestrator", f"Agent {agent_name} not found for kill switch access.")

    def create_agent(self, agent_type: str, parent_agent_name: str = None, creator_agent_type: str = None):
        """
        Dynamically generates a "cool name" for the new agent and adds it to the managed agents list.
        Instantiates the correct agent type based on the agent_type string.

        Args:
            agent_type (str): The type of agent to create (e.g., "SearchAgent", "PupAgent").
            parent_agent_name (str, optional): The name of the parent agent if this is a sub-agent. Defaults to None.
            creator_agent_type (str, optional): The type of agent attempting to create this new agent. Used for sub-agent creation lock. Defaults to None.
        """
        agent_classes = {
            "AgentBase": AgentBase,
            "SearchAgent": SearchAgent,
            "ProgrammingAgent": ProgrammingAgent,
            "DebuggingOptimizationAgent": DebuggingOptimizationAgent,
            "CommunicationsAgent": CommunicationsAgent,
            "SelfUpgradingAgent": SelfUpgradingAgent,
            "LoveloAgent": LoveloAgent,
            "UploadAgent": UploadAgent,
            "SentinelAgent": SentinelAgent,
            "HoundAgent": HoundAgent,
            "PupAgent": PupAgent,
            "ReconnaissanceAgent": ReconnaissanceAgent,
        }

        if agent_type not in agent_classes:
            print(f"Orchestrator Error: Unknown agent type '{agent_type}'.")
            self.error_logger.log_error("Orchestrator", f"Unknown agent type: {agent_type}")
            return None

        cool_names = ["Chimera", "Sentinel", "Nova", "Phoenix", "Vanguard", "Specter", "Oracle", "Echo"]
        name = f"{random.choice(cool_names)}_{self.agent_name_counter}"
        self.agent_name_counter += 1

        AgentClass = agent_classes[agent_type]
        is_sub_agent = agent_type in ["HoundAgent", "PupAgent"] # Define sub-agent types

        # Sub-Agent Creation Lock Logic
        if is_sub_agent and not self.sub_agent_creation_allowed:
            if creator_agent_type not in self.main_agents:
                error_msg = f"Sub-agent creation by {creator_agent_type} is locked. Only main agents or Sentinel can create sub-agents when locked."
                print(f"Orchestrator Error: {error_msg}")
                self.error_logger.log_error("Orchestrator", error_msg)
                if self.gui_ref:
                    self.gui_ref.update_error_display("Orchestrator", error_msg)
                return None

        new_agent = AgentClass(name=name, orchestrator=self, is_sub_agent=is_sub_agent, parent_agent_name=parent_agent_name)
        self.managed_agents.append(new_agent)
        self.active_agents_count += 1

        # Update agent hierarchy
        if parent_agent_name:
            if parent_agent_name not in self.agent_hierarchy:
                self.agent_hierarchy[parent_agent_name] = []
            self.agent_hierarchy[parent_agent_name].append(new_agent.name)
            # Also update the parent agent's internal sub_agents list
            for agent in self.managed_agents:
                if agent.name == parent_agent_name:
                    agent.add_sub_agent(new_agent.name)
                    break

        if agent_type == "SearchAgent":
            self.active_search_agents_count += 1

        print(f"Orchestrator: Created new agent: {new_agent.name} of type {agent_type} (Parent: {parent_agent_name})")
        if self.gui_ref:
            self.gui_ref.add_agent_monitoring_row(new_agent.name)
            self.gui_ref.update_agent_counts(self.active_agents_count, self.active_search_agents_count)
            self.gui_ref.update_agent_hierarchy_display(self.agent_hierarchy) # Update GUI hierarchy
        return new_agent

    def handle_error(self, agent_name: str, error_message: str, details: dict = None):
        """
        Receives and handles errors from agents.
        Logs the error and notifies the GUI, including an option for Auto-Fix.
        """
        print(f"Orchestrator received error from {agent_name}: {error_message}")
        self.error_logger.log_error(agent_name, error_message, details) # Log the error

        if self.gui_ref:
            # Pass error details to GUI for display and Auto-Fix button
            error_data = {
                "agent_name": agent_name,
                "message": error_message,
                "details": details,
                "auto_fix_available": True,
                "fixed": False # Initially not fixed
            }
            self.gui_ref.display_error(error_data)

        # Placeholder for initial Auto-Fix logic (Orchestrator's internal analysis)
        print(f"Orchestrator: Considering auto-fix for error from {agent_name}.")
        if details:
            print(f"Orchestrator: Error details for auto-fix consideration: {details}")

    def trigger_auto_fix(self, agent_name: str, error_message: str, details: dict = None):
        """
        Initiates the Auto-Fix process for a given error, typically triggered by the GUI.
        """
        print(f"Orchestrator: Auto-Fix triggered for error from {agent_name}: {error_message}")
        if details:
            print(f"Orchestrator: Auto-Fix details: {details}")

        # Placeholder for actual Auto-Fix logic
        # This is where the Orchestrator would analyze the error and potentially
        # assign a task to a ProgrammingAgent or DebuggingOptimizationAgent.
        # For now, we'll just print a message and simulate a fix attempt.
        fix_attempt_message = f"Orchestrator attempting to auto-fix error from {agent_name}..."
        print(fix_attempt_message)
        if self.gui_ref:
            self.gui_ref.update_chat_display("Orchestrator", fix_attempt_message)
            # Simulate a "fix" by updating the error display (e.g., marking as resolved)
            fixed_error_data = {
                "agent_name": agent_name,
                "message": error_message,
                "details": details,
                "auto_fix_available": False,
                "fixed": True
            }
            self.gui_ref.display_error(fixed_error_data)

        # Example: Assigning a task to a hypothetical ProgrammingAgent
        # if self.programming_agent_instance: # Assuming you have a reference to such an agent
        #     self.assign_task(self.programming_agent_instance, f"Fix error in {agent_name}: {error_message}")

    def handle_message(self, sender_agent_name: str, message_payload: dict):
        """
        Receives and handles messages from agents via NexusLight, forwarding them to the GUI.
        Processes task-related messages.
        """
        message_type = message_payload.get("type")
        message_content = message_payload.get("content")

        print(f"Orchestrator received message from {sender_agent_name} (Type: {message_type}): {message_content}")
        if self.gui_ref:
            # Send all messages to the chat display
            self.gui_ref.update_chat_display(sender_agent_name, f"[{message_type}] {message_content}")

            # Also send relevant messages to the interactive terminal for overall system activity/debugging
            if message_type in ["error", "system_status", "task_assigned", "task_completed", "no_task_available"]:
                self.gui_ref.append_terminal_output(f"Orchestrator (from {sender_agent_name}): [{message_type}] {message_content}", output_type="System")

        if message_type == "request_task":
            task = self.get_available_task(sender_agent_name)
            if task:
                # Send the task back to the agent via NexusLight (simulated)
                self.nexus_light.send_message(
                    recipient_agent_name=sender_agent_name,
                    message_payload={"type": "task_assigned", "task": task}
                )
            else:
                self.nexus_light.send_message(
                    recipient_agent_name=sender_agent_name,
                    message_payload={"type": "no_task_available", "content": "No tasks currently available."}
                )
        elif message_type == "task_completed":
            task_id = message_payload.get("task_id")
            status = message_payload.get("status", "completed")
            if task_id:
                self.update_task_status(task_id, status, sender_agent_name)
            else:
                print(f"Orchestrator Error: 'task_completed' message from {sender_agent_name} missing task_id.")
                self.error_logger.log_error("Orchestrator", f"'task_completed' message missing task_id from {sender_agent_name}")
        # Add other message types as needed
        elif message_type == "upgrade_proposal":
            agent_name = message_payload.get("agent")
            upgrade_details = message_payload.get("upgrade_details")
            if agent_name and upgrade_details:
                self.receive_upgrade_proposal(agent_name, upgrade_details)
            else:
                print(f"Orchestrator Error: 'upgrade_proposal' message from {sender_agent_name} missing agent name or upgrade details.")
                self.error_logger.log_error("Orchestrator", f"'upgrade_proposal' message missing details from {sender_agent_name}")
        elif message_type == "upgrade_status_report":
            agent_name = message_payload.get("agent")
            status = message_payload.get("status")
            details = message_payload.get("details")
            if agent_name and status:
                print(f"Orchestrator received upgrade status report from {agent_name}: {status}")
                if details:
                    print(f"Details: {details}")
                # Placeholder for handling status reports (e.g., updating GUI, deciding next steps)
                if self.gui_ref:
                    self.gui_ref.update_chat_display("Orchestrator", f"Received upgrade status '{status}' from {agent_name}.")
            else:
                print(f"Orchestrator Error: 'upgrade_status_report' message from {sender_agent_name} missing agent name or status.")
                self.error_logger.log_error("Orchestrator", f"'upgrade_status_report' message missing details from {sender_agent_name}")

    def receive_performance_report(self, agent_name: str, performance_data: dict):
        """
        Receives simulated performance data from agents.
        """
        print(f"Orchestrator: Received performance report from {agent_name}: {performance_data}")
        if self.gui_ref:
            self.gui_ref.update_chat_display("Orchestrator", f"Performance report from {agent_name}: CPU={performance_data.get('agent_cpu', 'N/A')}, Mem={performance_data.get('agent_memory', 'N/A')}")
            # Update GUI's agent monitoring dashboard with performance data
            self.gui_ref.update_agent_performance_metrics(agent_name, performance_data)

        # Simulate assigning optimization tasks if performance is poor
        # For simplicity, let's say if agent_cpu > 10% or agent_memory > 200MB, assign optimization
        try:
            agent_cpu_val = float(performance_data.get("agent_cpu", "0%").replace("%", ""))
            agent_mem_val = float(performance_data.get("agent_memory", "0MB").replace("MB", ""))

            if agent_cpu_val > 10 or agent_mem_val > 200:
                print(f"Orchestrator: Detected potential performance issue for {agent_name}. Assigning optimization task.")
                # Find DebuggingOptimizationAgent
                debugger_agent = next((agent for agent in self.managed_agents if isinstance(agent, DebuggingOptimizationAgent)), None)
                if debugger_agent:
                    task_description = f"Optimize performance for {agent_name} based on report: CPU={agent_cpu_val}%, Mem={agent_mem_val}MB"
                    self.assign_task(debugger_agent.name, task_description)
                else:
                    print("Orchestrator: No DebuggingOptimizationAgent found to assign optimization task.")
        except ValueError:
            print(f"Orchestrator: Could not parse performance data for {agent_name}.")

    def receive_bottleneck_report(self, agent_name: str, bottleneck_details: str):
        """
        Receives simulated bottleneck reports from the Debugging/OptimizationAgent.
        """
        print(f"Orchestrator: Received bottleneck report from {agent_name}: {bottleneck_details}")
        if self.gui_ref:
            self.gui_ref.update_chat_display("Orchestrator", f"Bottleneck report from {agent_name}: {bottleneck_details}")
            # Potentially update a dedicated GUI section for bottlenecks
            self.gui_ref.append_terminal_output(f"Orchestrator: Bottleneck detected by {agent_name}: {bottleneck_details}", output_type="Warning")

        # Simulate assigning optimization tasks based on bottleneck details
        if "CPU usage" in bottleneck_details or "memory usage" in bottleneck_details:
            print(f"Orchestrator: Bottleneck '{bottleneck_details}' indicates need for optimization. Assigning task.")
            debugger_agent = next((agent for agent in self.managed_agents if isinstance(agent, DebuggingOptimizationAgent)), None)
            if debugger_agent:
                task_description = f"Address bottleneck: {bottleneck_details}"
                self.assign_task(debugger_agent.name, task_description)
            else:
                print("Orchestrator: No DebuggingOptimizationAgent found to assign bottleneck resolution task.")

    def receive_optimization_results(self, agent_name: str, optimization_results: dict):
        """
        Receives simulated optimization results from the Debugging/OptimizationAgent.
        """
        print(f"Orchestrator: Received optimization results from {agent_name}: {optimization_results}")
        if self.gui_ref:
            self.gui_ref.update_chat_display("Orchestrator", f"Optimization results from {agent_name}: {optimization_results.get('optimization_suggestions', 'No suggestions')}")
            # Potentially update GUI to show optimized code or status
            self.gui_ref.append_terminal_output(f"Orchestrator: Optimization results from {agent_name}: {optimization_results.get('optimization_suggestions', 'No suggestions')}", output_type="System")

        # Simulate reviewing results and updating system state or re-testing
        if "optimized_code" in optimization_results and optimization_results.get("optimized_code"):
            print(f"Orchestrator: Simulating review of optimized code. Consider re-testing.")
            # In a real scenario, this might trigger a re-test task for the SelfUpgradingAgent
            # or ProgrammingAgent to apply the changes.

    def receive_upgrade_proposal(self, agent_name: str, upgrade_details: dict):
        """
        Receives upgrade proposals from SelfUpgradingAgents.
        Simulates reviewing the proposal, potentially assigning testing, and notifying the GUI.
        """
        print(f"Orchestrator: Received upgrade proposal from {agent_name}: {upgrade_details.get('name', 'Unknown Upgrade')}")
        if self.gui_ref:
            self.gui_ref.update_chat_display("Orchestrator", f"Received upgrade proposal from {agent_name}: {upgrade_details.get('name', 'Unknown Upgrade')}")
            # Notify GUI about the proposed upgrade (placeholder for a dedicated GUI element)
            self.gui_ref.append_terminal_output(f"Orchestrator: New Upgrade Proposal from {agent_name}: {upgrade_details.get('name', 'Unknown Upgrade')}", output_type="System")

        # Simulate reviewing the proposal
        print(f"Orchestrator: Simulating review of proposal: {upgrade_details}")
        time.sleep(1) # Simulate review time

        # Simulate more detailed review process
        print(f"Orchestrator: Simulating compatibility check for upgrade: {upgrade_details.get('name')}")
        print(f"Orchestrator: Simulating risk assessment for upgrade: {upgrade_details.get('name')}")
        time.sleep(1)

        # Simulate deciding whether to assign for testing
        assign_for_testing = random.choice([True, False])

        if assign_for_testing:
            print(f"Orchestrator: Proposal '{upgrade_details.get('name', 'Unknown Upgrade')}' accepted for testing.")
            # Simulate assigning the testing task to a Debugging/OptimizationAgent or SelfUpgradingAgent
            tester_agent = random.choice([agent for agent in self.managed_agents if isinstance(agent, (DebuggingOptimizationAgent, SelfUpgradingAgent))]) # Find a suitable agent
            if tester_agent:
                task_description = f"Test proposed upgrade: {upgrade_details.get('name', 'Unknown Upgrade')}"
                task_id = self.assign_task(tester_agent.name, task_description, initial_status="pending")
                print(f"Orchestrator: Assigned upgrade testing task (ID: {task_id}) to {tester_agent.name}.")
                if self.gui_ref:
                    self.gui_ref.update_chat_display("Orchestrator", f"Assigned upgrade testing task to {tester_agent.name}.")
            else:
                print("Orchestrator: No suitable agent found to assign upgrade testing task.")
                self.error_logger.log_error("Orchestrator", "No suitable agent found for upgrade testing task.")
                if self.gui_ref:
                    self.gui_ref.update_error_display("Orchestrator", "No suitable agent found for upgrade testing task.")
        else:
            print(f"Orchestrator: Proposal '{upgrade_details.get('name', 'Unknown Upgrade')}' rejected for testing (simulated).")
            # Simulate notifying the proposing agent (optional)
            # self.nexus_light.send_message(agent_name, {"type": "proposal_rejected", "details": "Simulated rejection"}) # Placeholder

    def approve_upgrade(self, upgrade_details: dict):
        """
        Simulates the Orchestrator's decision to approve an upgrade.
        This would typically happen after successful testing.
        """
        print(f"Orchestrator: Simulating approval of upgrade: {upgrade_details.get('name', 'Unknown Upgrade')}")
        if self.gui_ref:
            self.gui_ref.update_chat_display("Orchestrator", f"Simulating approval of upgrade: {upgrade_details.get('name', 'Unknown Upgrade')}")
            self.gui_ref.append_terminal_output(f"Orchestrator: Upgrade Approved: {upgrade_details.get('name', 'Unknown Upgrade')}", output_type="System")

        # Simulate approval process
        print(f"Orchestrator: Simulating deployment preparation for upgrade: {upgrade_details.get('name')}")
        print(f"Orchestrator: Simulating notification to all agents about upcoming upgrade: {upgrade_details.get('name')}")
        time.sleep(1)
        print(f"Orchestrator: Simulated upgrade approval complete for: {upgrade_details.get('name')}")

    def reject_upgrade(self, upgrade_details: dict):
        """
        Simulates the Orchestrator's decision to reject an upgrade.
        This would typically happen after failed testing or review.
        """
        print(f"Orchestrator: Simulating rejection of upgrade: {upgrade_details.get('name', 'Unknown Upgrade')}")
        if self.gui_ref:
            self.gui_ref.update_chat_display("Orchestrator", f"Simulating rejection of upgrade: {upgrade_details.get('name', 'Unknown Upgrade')}")
            self.gui_ref.append_terminal_output(f"Orchestrator: Upgrade Rejected: {upgrade_details.get('name', 'Unknown Upgrade')}", output_type="System")

        # Simulate rejection process
        print(f"Orchestrator: Simulating notification to proposing agent about rejection: {upgrade_details.get('name')}")
        print(f"Orchestrator: Simulating logging of rejection reason for: {upgrade_details.get('name')}")
        time.sleep(1)
        print(f"Orchestrator: Simulated upgrade rejection complete for: {upgrade_details.get('name')}")

    def adjust_agent_count(self, desired_total_agents: int):
        """
        Adjusts the total number of agents and ensures the Search Agent ratio is maintained.
        For every 5 agents, there should be 1 dedicated search agent.
        """
        if desired_total_agents < 0:
            print("Orchestrator Error: Desired total agents cannot be negative.")
            self.error_logger.log_error("Orchestrator", "Desired total agents cannot be negative.")
            return

        print(f"Orchestrator: Adjusting agent count to {desired_total_agents}...")

        # Calculate required Search Agents
        required_search_agents = desired_total_agents // 5
        if desired_total_agents % 5 != 0 and desired_total_agents > 0:
            required_search_agents += 1 # Ensure at least one search agent if total agents > 0

        print(f"Current agents: {self.active_agents_count}, Current Search Agents: {self.active_search_agents_count}")
        print(f"Desired total agents: {desired_total_agents}, Required Search Agents: {required_search_agents}")

        # Add or remove agents to reach desired total
        if self.active_agents_count < desired_total_agents:
            agents_to_add = desired_total_agents - self.active_agents_count
            print(f"Orchestrator: Adding {agents_to_add} new agents.")
            for _ in range(agents_to_add):
                # Prioritize adding Search Agents if needed
                if self.active_search_agents_count < required_search_agents:
                    self.create_agent("SearchAgent")
                else:
                    # For now, create a generic agent. In a real scenario,
                    # the Orchestrator would decide based on task requirements.
                    self.create_agent("AgentBase")
        elif self.active_agents_count > desired_total_agents:
            agents_to_remove = self.active_agents_count - desired_total_agents
            print(f"Orchestrator: Identifying {agents_to_remove} agents for termination.")
            # Placeholder for termination logic:
            # In a real system, this would involve more complex logic to decide which agents to terminate.
            # For now, we'll just print a message.
            for _ in range(agents_to_remove):
                if self.active_agents_count > 0:
                    # Simple removal: remove the last agent added for now
                    agent_to_terminate = self.managed_agents[-1]
                    self.remove_agent(agent_to_terminate.name)
                else:
                    break # No agents left to remove

        # Ensure Search Agent ratio is met after total agent adjustment
        # This handles cases where total agents might be reduced, but search agents are still too many
        while self.active_search_agents_count > required_search_agents:
            # Identify a SearchAgent to remove
            search_agent_to_remove = None
            for agent in self.managed_agents:
                if isinstance(agent, SearchAgent): # Check if it's a SearchAgent instance
                    search_agent_to_remove = agent
                    break
            if search_agent_to_remove:
                print(f"Orchestrator: Removing excess Search Agent: {search_agent_to_remove.name}")
                self.remove_agent(search_agent_to_remove.name)
            else:
                break # No Search Agents found to remove, something is off

        print(f"Orchestrator: Agent count adjustment complete. Total agents: {self.active_agents_count}, Search Agents: {self.active_search_agents_count}")
        if self.gui_ref:
            self.gui_ref.update_agent_counts(self.active_agents_count, self.active_search_agents_count)

    def remove_agent(self, agent_name: str):
        """
        Placeholder method to handle agent termination and update counts.
        In a real implementation, this would involve stopping the agent's process,
        cleaning up resources, and removing it from managed_agents.
        """
        agent_to_remove = None
        for agent in self.managed_agents:
            if agent.name == agent_name:
                agent_to_remove = agent
                break

        if agent_to_remove:
            print(f"Orchestrator: Terminating agent: {agent_to_remove.name}")
            # Simulate termination
            if hasattr(agent_to_remove, 'kill'):
                agent_to_remove.kill() # Call kill switch if available

            self.managed_agents.remove(agent_to_remove)
            self.active_agents_count -= 1
            if isinstance(agent_to_remove, SearchAgent):
                self.active_search_agents_count -= 1

            print(f"Orchestrator: Agent {agent_name} terminated. Current total agents: {self.active_agents_count}, Search Agents: {self.active_search_agents_count}")
            if self.gui_ref:
                self.gui_ref.remove_agent_monitoring_row(agent_name)
                self.gui_ref.update_agent_counts(self.active_agents_count, self.active_search_agents_count)
                # Remove from hierarchy
                for parent, children in list(self.agent_hierarchy.items()):
                    if agent_name in children:
                        self.agent_hierarchy[parent].remove(agent_name)
                        if not self.agent_hierarchy[parent]: # If parent has no more children, remove parent entry
                            del self.agent_hierarchy[parent]
                        break
                self.gui_ref.update_agent_hierarchy_display(self.agent_hierarchy) # Update GUI hierarchy
        else:
            print(f"Orchestrator: Agent {agent_name} not found for termination.")
            if self.gui_ref:
                self.gui_ref.update_error_display("Orchestrator", f"Agent {agent_name} not found for termination.")

    def get_system_metrics(self):
        """
        Simulates gathering system-wide metrics (CPU, memory, NVMe, GPU, network, uptime).
        Returns a dictionary of simulated metric values.
        """
        # Simulate CPU usage (0-100%)
        cpu_usage = f"{random.uniform(10.0, 80.0):.2f}%"
        # Simulate Memory usage (e.g., 20GB / 64GB)
        memory_total = 64 # GB
        memory_used = random.uniform(8.0, 48.0)
        memory_usage = f"{memory_used:.2f}GB / {memory_total}GB"
        # Simulate NVMe Swap Space Utilization (0-100%)
        nvme_swap = f"{random.uniform(0.0, 15.0):.2f}%"
        # Simulate GPU Utilization (0-100%)
        gpu_usage = f"{random.uniform(0.0, 90.0):.2f}%"
        # Simulate Network Usage (KB/s)
        network_in = f"{random.uniform(100.0, 1000.0):.2f}KB/s"
        network_out = f"{random.uniform(50.0, 500.0):.2f}KB/s"
        # Simulate System Uptime
        uptime_seconds = int(time.time() - self.start_time)
        hours, remainder = divmod(uptime_seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        system_uptime = f"{hours:02}:{minutes:02}:{seconds:02}"

        return {
            "cpu": cpu_usage,
            "memory": memory_usage,
            "nvme": nvme_swap,
            "gpu": gpu_usage,
            "network_in": network_in,
            "network_out": network_out,
            "uptime": system_uptime
        }

    def _periodic_system_metrics_update(self):
        """
        Called periodically by the QTimer to update system metrics in the GUI.
        """
        if self.gui_ref:
            metrics = self.get_system_metrics()
            self.gui_ref.update_system_metrics(
                metrics["cpu"],
                metrics["memory"],
                metrics["nvme"],
                metrics["gpu"],
                metrics["network_in"],
                metrics["network_out"],
                metrics["uptime"]
            )
            # Also update total agents count here, as it's part of system overview
            self.gui_ref.update_agent_counts(self.active_agents_count, self.active_search_agents_count)

            # Update task board and overall progress periodically
            self.gui_ref.update_task_board(self.get_all_tasks_for_gui())
            self.gui_ref.update_overall_progress(self.get_overall_project_progress())

    def run(self):
        """
        Basic run method for the Orchestrator's operational loop.
        """
        print("Orchestrator: Starting operational loop...")
        # This loop would typically manage agents, assign tasks, etc.
        # For now, it's a placeholder. The QTimer handles periodic GUI updates.
        while True:
            self.manage_projects()
            # Example: Check for idle agents and assign tasks
            for agent in self.managed_agents:
                if agent.status == "idle":
                    print(f"Orchestrator: Agent {agent.name} is idle. Considering new tasks.")
                    # self.assign_task(agent, "Perform a general check") # Example task assignment
            time.sleep(5) # Simulate work, but GUI updates are on a separate timer

    def revert_codebase(self):
        """
        Placeholder method to simulate reverting the codebase to a last stable version.
        """
        print("Orchestrator: Simulating codebase revert to last stable version...")
        if self.gui_ref:
            self.gui_ref.append_terminal_output("Orchestrator: Codebase reverted to last stable version (simulated).", output_type="System")
            self.gui_ref.update_git_status(self.get_git_status()) # Update Git status after revert

    def get_git_status(self) -> str:
        """
        Placeholder method to simulate retrieving Git status.
        """
        statuses = [
            "Git Status: Clean",
            "Git Status: Pending Changes: main_gui.py, orchestrator.py",
            "Git Status: Pending Changes: self_upgrading_agent.py",
            "Git Status: Branch: feature/gui-updates, Ahead by 2 commits"
        ]
        return random.choice(statuses)

    def get_sandbox_performance_data(self) -> dict:
        """
        Placeholder method to simulate retrieving sandbox performance data.
        """
        latency = f"{random.randint(50, 500)}ms"
        cpu = f"{random.randint(10, 90)}%"
        ram = f"{random.randint(100, 1024)}MB"
        status = random.choice(["Success", "Failure", "Running Tests..."])
        return {"latency": latency, "cpu": cpu, "ram": ram, "status": status}

    # def _periodic_sandbox_git_update(self): # Removed as per user feedback
    #     """
    #     Called periodically by the QTimer to update sandbox performance and Git status in the GUI.
    #     """
    #     if self.gui_ref:
    #         # Update Sandbox Performance
    #         sandbox_data = self.get_sandbox_performance_data()
    #         self.gui_ref.update_sandbox_performance(
    #             sandbox_data["latency"],
    #             sandbox_data["cpu"],
    #             sandbox_data["ram"],
    #             sandbox_data["status"]
    #         )

    #         # Update Git Status
    #         git_status = self.get_git_status()
    #         self.gui_ref.update_git_status(git_status)

    def handle_new_device_detection(self, device_info: dict):
        """
        Placeholder method to handle new device detection from HardwareMonitor.
        This method determines if it's a personal device, asks for permission,
        and assigns a task to a relevant agent for optimization.
        """
        print(f"Orchestrator: Handling new device detection: {device_info['name']}")

        # Placeholder for determining if it's a personal device
        is_personal = device_info.get("is_personal", False)

        if is_personal:
            print(f"Orchestrator: Device '{device_info['name']}' is a personal device. "
                  "Asking for user/Orchestrator permission (via GUI or Nexus Light)...")
            # Logic to interact with GUI or Nexus Light for permission
            if self.gui_ref:
                self.gui_ref.update_chat_display("Orchestrator",
                    f"Permission required for personal device: {device_info['name']}. Optimize?")
            # In a real scenario, this would involve a blocking call or a callback
            # from the GUI/NexusLight after user interaction.
        else:
            print(f"Orchestrator: Device '{device_info['name']}' is not personal. Proceeding with optimization.")

        # Placeholder for assigning a task to a relevant agent (e.g., Programming Agent)
        # For example, if a ProgrammingAgent instance exists:
        # if self.programming_agent_instance:
        #     self.assign_task(self.programming_agent_instance,
        #                      f"Optimize hardware device: {device_info['name']}")
        print(f"Orchestrator: Assigning task to relevant agent for optimization of {device_info['name']} (placeholder).")

    def customize_os_file(self, file_path: str, content: str):
        """
        Placeholder method representing the Orchestrator's capability to edit system files.
        Includes a critical check to prevent editing networking files, which are exclusive
        to the Search Agent. This is a highly sensitive operation.
        """
        print(f"Orchestrator: Attempting to customize OS file: {file_path}")

        # Critical security check: Prevent editing networking files
        networking_file_patterns = [
            "network", "netplan", "resolv.conf", "hosts", "interfaces", "firewall", "iptables"
        ] # Common networking file keywords/names

        if any(pattern in file_path.lower() for pattern in networking_file_patterns):
            print(f"Orchestrator: WARNING! Attempted to edit a networking file ({file_path}). "
                  "This operation is exclusive to the Search Agent and has been blocked.")
            self.error_logger.log_error("Orchestrator",
                                        f"Blocked attempt to edit networking file: {file_path}",
                                        {"file_path": file_path, "reason": "Networking files are Search Agent exclusive"})
            # Potentially delegate to Search Agent or log a security alert
            return False

        print(f"Orchestrator: Customizing OS file '{file_path}' with new content (placeholder for sensitive operation).")
        # In a real implementation, this would involve writing to the system file
        # with appropriate permissions. This is a highly privileged operation.
        # Example:
        # try:
        #     with open(file_path, 'w') as f:
        #         f.write(content)
        #     print(f"Orchestrator: Successfully customized {file_path}.")
        #     return True
        # except Exception as e:
        #     print(f"Orchestrator: Failed to customize {file_path}: {e}")
        #     self.error_logger.log_error("Orchestrator",
        #                                 f"Failed to customize OS file: {file_path}",
        #                                 {"file_path": file_path, "error": str(e)})
        #     return False
        return True # Simulate success for now

    def get_agent_team_count(self, agent_name: str) -> int:
        """
        Returns the count of direct sub-agents for a given agent.
        """
        return len(self.agent_hierarchy.get(agent_name, []))

    def set_sub_agent_creation_lock(self, status: bool):
        """
        Sets the status of the sub-agent creation lock.
        True means sub-agent creation is allowed for all.
        False means only main agents/Sentinel can create sub-agents.
        """
        self.sub_agent_creation_allowed = status
        print(f"Orchestrator: Sub-Agent Creation Lock set to: {'OFF (Allowed)' if status else 'ON (Restricted)'}")
        if self.gui_ref:
            self.gui_ref.update_chat_display("Orchestrator", f"Sub-Agent Creation Lock set to: {'OFF (Allowed)' if status else 'ON (Restricted)'}")
            self.gui_ref.update_sub_agent_lock_status(status)
            self.gui_ref.append_terminal_output(f"Sub-Agent Creation Lock set to: {'OFF (Allowed)' if status else 'ON (Restricted)'}.", output_type="System")

    def handle_user_command(self, command: str):
        """
        Receives and processes user commands from the GUI's interactive terminal.
        Simulates processing and sends simulated output back to the GUI.
        """
        print(f"Orchestrator received user command: {command}")
        if self.gui_ref:
            self.gui_ref.append_terminal_output(f"Orchestrator: Processing command: '{command}'...", output_type="Narrative")

            # Simulate command processing and delegation
            if "create agent" in command.lower():
                parts = command.lower().split("create agent ")
                if len(parts) > 1:
                    agent_type = parts[1].strip().replace(" ", "") # Remove spaces for class name
                    # Capitalize first letter for class name
                    agent_type = agent_type[0].upper() + agent_type[1:] if agent_type else ""
                    if agent_type:
                        self.gui_ref.append_terminal_output(f"Orchestrator: Attempting to create a new {agent_type}...", output_type="System")
                        new_agent = self.create_agent(agent_type)
                        if new_agent:
                            self.gui_ref.append_terminal_output(f"Orchestrator: Successfully created {new_agent.name} of type {agent_type}.", output_type="System")
                        else:
                            self.gui_ref.append_terminal_output(f"Orchestrator: Failed to create {agent_type}. Check logs for details.", output_type="Error")
                    else:
                        self.gui_ref.append_terminal_output("Orchestrator: Please specify an agent type (e.g., 'create agent SearchAgent').", output_type="Error")
                else:
                    self.gui_ref.append_terminal_output("Orchestrator: Invalid 'create agent' command. Usage: 'create agent [AgentType]'.", output_type="Error")
            elif "list agents" in command.lower():
                agent_names = [agent.name for agent in self.managed_agents]
                if agent_names:
                    self.gui_ref.append_terminal_output(f"Orchestrator: Currently managed agents: {', '.join(agent_names)}", output_type="System")
                else:
                    self.gui_ref.append_terminal_output("Orchestrator: No agents are currently managed.", output_type="System")
            elif "kill agent" in command.lower():
                parts = command.lower().split("kill agent ")
                if len(parts) > 1:
                    agent_name_to_kill = parts[1].strip()
                    self.gui_ref.append_terminal_output(f"Orchestrator: Attempting to kill agent: {agent_name_to_kill}...", output_type="System")
                    self.access_kill_switch(agent_name_to_kill) # Use existing kill switch method
                else:
                    self.gui_ref.append_terminal_output("Orchestrator: Invalid 'kill agent' command. Usage: 'kill agent [AgentName]'.", output_type="Error")
            elif "adjust agents" in command.lower():
                try:
                    desired_count = int(command.lower().replace("adjust agents", "").strip())
                    self.gui_ref.append_terminal_output(f"Orchestrator: Adjusting total agents to {desired_count}...", output_type="System")
                    self.adjust_agent_count(desired_count)
                except ValueError:
                    self.gui_ref.append_terminal_output("Orchestrator: Invalid number for 'adjust agents'. Usage: 'adjust agents [number]'.", output_type="Error")
            elif "toggle sub-agent lock" in command.lower():
                current_status = self.sub_agent_creation_allowed
                new_status = not current_status
                self.set_sub_agent_creation_lock(new_status)
                self.gui_ref.append_terminal_output(f"Orchestrator: Sub-Agent Creation Lock toggled to {'OFF (Allowed)' if new_status else 'ON (Restricted)'}.", output_type="System")
            else:
                self.gui_ref.append_terminal_output(f"Orchestrator: Unknown command: '{command}'. Try 'create agent [AgentType]', 'list agents', 'kill agent [AgentName]', 'adjust agents [number]', or 'toggle sub-agent lock'.", output_type="Error")

        # Placeholder for actual delegation to agents
        # For now, we just simulate the response.
        # In a real scenario, the Orchestrator would parse the command and
        # assign a task to an appropriate agent.
# Placeholder methods for Settings & Configuration Panel
    def get_api_keys(self) -> list:
        """Simulates retrieving API keys."""
        return self.api_keys

    def add_api_key(self, key: str):
        """Simulates adding an API key."""
        self.api_keys.append(key)
        print(f"Orchestrator: Simulated adding API key: {key[:5]}...")

    def edit_api_key(self, old_key: str, new_key: str):
        """Simulates editing an API key."""
        try:
            idx = self.api_keys.index(old_key)
            self.api_keys[idx] = new_key
            print(f"Orchestrator: Simulated editing API key from {old_key[:5]}... to {new_key[:5]}...")
        except ValueError:
            print(f"Orchestrator: API key {old_key[:5]}... not found for editing.")

    def delete_api_key(self, key: str):
        """Simulates deleting an API key."""
        try:
            self.api_keys.remove(key)
            print(f"Orchestrator: Simulated deleting API key: {key[:5]}...")
        except ValueError:
            print(f"Orchestrator: API key {key[:5]}... not found for deletion.")

    def get_llm_selection(self) -> dict:
        """Simulates retrieving LLM selection settings."""
        return self.llm_selection

    def set_llm_selection(self, primary_llm: str, sub_agent_default: str):
        """Simulates setting LLM selection."""
        self.llm_selection["primary"] = primary_llm
        self.llm_selection["sub_agent_default"] = sub_agent_default
        print(f"Orchestrator: Simulated setting LLM selection: Primary={primary_llm}, Sub-Agent Default={sub_agent_default}")

    def get_theme_settings(self) -> dict:
        """Simulates retrieving theme settings."""
        return self.theme_settings

    def set_theme_settings(self, accent_color: str, dark_mode_intensity: int):
        """Simulates setting theme settings."""
        self.theme_settings["accent_color"] = accent_color
        self.theme_settings["dark_mode_intensity"] = dark_mode_intensity
        print(f"Orchestrator: Simulated setting theme: Accent={accent_color}, Dark Mode={dark_mode_intensity}%")

    def get_persistence_settings(self) -> dict:
        """Simulates retrieving persistence settings."""
        return self.persistence_settings

    def set_persistence_settings(self, auto_save: bool, backups: bool):
        """Simulates setting persistence settings."""
        self.persistence_settings["auto_save"] = auto_save
        self.persistence_settings["backups"] = backups
        print(f"Orchestrator: Simulated setting persistence: Auto-Save={auto_save}, Backups={backups}")

    def get_log_configuration(self) -> dict:
        """Simulates retrieving log configuration."""
        return self.log_config

    def set_log_configuration(self, verbosity: str, retention_days: int):
        """Simulates setting log configuration."""
        self.log_config["verbosity"] = verbosity
        self.log_config["retention_days"] = retention_days
        print(f"Orchestrator: Simulated setting log config: Verbosity={verbosity}, Retention={retention_days} days")

    def get_notification_preferences(self) -> dict:
        """Simulates retrieving notification preferences."""
        return self.notification_prefs

    def set_notification_preferences(self, on_errors: bool, on_status_changes: bool):
        """Simulates setting notification preferences."""
        self.notification_prefs["on_errors"] = on_errors
        self.notification_prefs["on_status_changes"] = on_status_changes
        print(f"Orchestrator: Simulated setting notifications: On Errors={on_errors}, On Status Changes={on_status_changes}")

    def receive_user_directive(self, directive_text: str):
        """
        Receives a new high-level directive from the GUI.
        Simulates breaking it down into smaller tasks and assigning them.
        """
        print(f"Orchestrator: Received new user directive: '{directive_text}'")
        if self.gui_ref:
            self.gui_ref.append_terminal_output(f"Orchestrator: Breaking down directive: '{directive_text}' into sub-tasks...", output_type="Narrative")

        # Simulate breaking down the directive into 2-3 smaller tasks
        simulated_tasks = [
            f"Analyze '{directive_text}' requirements",
            f"Develop initial plan for '{directive_text}'",
            f"Assign agents for '{directive_text}' execution"
        ]

        for i, task_desc in enumerate(simulated_tasks):
            # Assign to a random existing agent or a placeholder agent
            assigned_agent = random.choice([agent.name for agent in self.managed_agents] + ["ProgrammingAgent", "SearchAgent"])
            # Simulate initial status and progress
            status = "pending" if i == 0 else "not_started"
            progress = 0
            self.assign_task(assigned_agent, task_desc, initial_status=status, initial_progress=progress)

        if self.gui_ref:
            self.gui_ref.append_terminal_output(f"Orchestrator: Simulated breakdown complete. {len(simulated_tasks)} tasks created.", output_type="System")
            self.gui_ref.update_task_board(self.get_all_tasks_for_gui()) # Ensure GUI updates

    def get_all_tasks_for_gui(self) -> list:
        """
        Returns the current state of all tasks for display in the GUI.
        """
        # Simulate progress updates for existing tasks
        for task_id, task_info in self.tasks.items():
            if task_info["status"] == "in_progress":
                # Increment progress, cap at 99% to simulate ongoing work
                current_progress = task_info.get("progress", 0)
                if current_progress < 99:
                    task_info["progress"] = min(99, current_progress + random.randint(1, 10))
                else:
                    # Once 99% is reached, randomly complete it
                    if random.random() < 0.2: # 20% chance to complete
                        task_info["status"] = "completed"
                        task_info["progress"] = 100
                        print(f"Orchestrator: Task {task_id} completed.")
                        if self.gui_ref:
                            self.gui_ref.update_chat_display("Orchestrator", f"Task '{task_info['description']}' (ID: {task_id}) completed.")
            elif task_info["status"] == "pending":
                # Simulate starting a pending task
                if random.random() < 0.3: # 30% chance to start
                    task_info["status"] = "in_progress"
                    print(f"Orchestrator: Task {task_id} started.")
                    if self.gui_ref:
                        self.gui_ref.update_chat_display("Orchestrator", f"Task '{task_info['description']}' (ID: {task_id}) started.")

        return list(self.tasks.values())

    def get_overall_project_progress(self) -> int:
        """
        Returns a simulated overall project progress percentage based on current tasks.
        """
        if not self.tasks:
            return 0

        total_progress = sum(task.get("progress", 0) for task in self.tasks.values())
        overall_progress = int(total_progress / len(self.tasks))
        return overall_progress

# Example Usage (for testing)
if __name__ == "__main__":
    from main_gui import AIAgentArmyGUI
    import sys
    from PyQt5.QtWidgets import QApplication
    import threading

    app = QApplication(sys.argv)
    gui = AIAgentArmyGUI()
    orchestrator = Orchestrator(gui_ref=gui)
    gui.orchestrator_ref = orchestrator # Pass orchestrator reference to GUI

    # Start the Orchestrator's run loop in a separate thread to avoid blocking the GUI
    orchestrator_thread = threading.Thread(target=orchestrator.run, daemon=True)
    orchestrator_thread.start()

    # Simulate some initial terminal output
    gui.append_terminal_output("Welcome to the AI Agent Army Control Panel!", output_type="System")
    gui.append_terminal_output("Type 'list agents' in the terminal input below to see active agents.", output_type="Narrative")
    gui.append_terminal_output("Example command: 'create agent SearchAgent'", output_type="Narrative")
    gui.append_terminal_output("Example command: 'adjust agents 5'", output_type="Narrative")
    gui.append_terminal_output("Example command: 'toggle sub-agent lock'", output_type="Narrative")

    gui.show()
    sys.exit(app.exec_())
    sys.exit(app.exec_())