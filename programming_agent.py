from agent_base import AgentBase
import os
from pathlib import Path

class ProgrammingAgent(AgentBase):
    """
    The Programming Agent is focused on code development and modification.
    It works in conjunction with the Debugging/Optimization Agent.
    """
    def __init__(self, name: str, orchestrator):
        super().__init__(name=name, orchestrator=orchestrator)
        # Placeholder for collaboration with the Debugging/Optimization Agent
        self.collaboration_status = "Ready for collaboration with Debugging/Optimization Agent"
        # Define the project root directory for safe file operations
        self.project_root = Path(os.getcwd()) # Assumes current working directory is the project root

    def generate_code(self, requirements: str) -> str:
        """
        Simulates code generation based on requirements, integrating with the RAG database.
        """
        print(f"ProgrammingAgent: Generating code based on requirements: '{requirements}'...")
        
        # Retrieve programming knowledge from RAG database
        rag_response = self.query_rag_database(f"python code example for {requirements}")
        
        # Simulate code generation using RAG response
        generated_code = f"# Generated code for: {requirements}\n"
        if rag_response:
            generated_code += f"# RAG knowledge applied: {rag_response}\n"
            generated_code += "def example_function():\n"
            generated_code += "    # This is a simulated function based on RAG knowledge.\n"
            generated_code += "    print('Hello from generated code!')\n"
            generated_code += "    return 'Success'\n"
        else:
            generated_code += "def placeholder_function():\n"
            generated_code += "    print('No specific RAG knowledge found, generating placeholder.')\n"
            generated_code += "    return 'Placeholder'\n"
            
        print("ProgrammingAgent: Code generation simulated.")
        return generated_code

    def edit_code(self, file_path: str, changes: str):
        """
        Simulates editing existing code in a specified file, integrating with the RAG database.
        """
        print(f"ProgrammingAgent: Editing code in {file_path} with changes: '{changes}'...")
        
        # Retrieve programming knowledge from RAG database for context
        rag_response = self.query_rag_database(f"best practices for editing {file_path} with {changes}")
        if rag_response:
            print(f"ProgrammingAgent: Applying RAG knowledge for editing: {rag_response}")

        # Simulate file editing by printing the action
        print(f"ProgrammingAgent: Simulated applying changes to {file_path}.")
        # In a real scenario, this would involve parsing, modifying, and writing back the file.

    def access_system_files(self, file_path: str, mode: str = 'r', content: str = None):
        """
        Simulates file operations (read, write, update, delete) within the project directory.
        Crucially, ensures these operations are confined to the project's directory for security.
        Includes simulated security checks for directory traversal and file permissions.
        """
        # Simulated security check: Validate file path for directory traversal
        if '..' in file_path or '/tmp/' in file_path or '/etc/' in file_path:
            print(f"ProgrammingAgent: SECURITY ALERT - Attempt to access restricted path: {file_path}")
            self.error_logger.log_error(
                agent_name=self.name,
                error_message="Attempted directory traversal or access to restricted path",
                details={"file_path": file_path, "attempted_mode": mode}
            )
            # Notify SentinelAgent (placeholder)
            self.nexus_light.send_message("SentinelAgent", {
                "type": "security_alert",
                "details": f"ProgrammingAgent attempted directory traversal or access to restricted path: {file_path}"
            })
            return None

        absolute_file_path = (self.project_root / file_path).resolve()

        # Security check: Ensure the resolved path is within the project root
        if not absolute_file_path.is_relative_to(self.project_root):
            print(f"ProgrammingAgent: SECURITY ALERT - Attempt to access file outside project directory: {file_path}")
            self.error_logger.log_error(
                agent_name=self.name,
                error_message="Attempted unauthorized file access",
                details={"file_path": file_path, "attempted_mode": mode}
            )
            # Notify SentinelAgent (placeholder)
            self.nexus_light.send_message("SentinelAgent", {
                "type": "security_alert",
                "details": f"ProgrammingAgent attempted unauthorized file access outside project directory: {file_path}"
            })
            return None

        # Simulated security check: Check file permissions based on a dummy permission system
        # For demonstration, let's assume only .py and .txt files are allowed to be modified
        if mode in ['w', 'a', 'd'] and not (file_path.endswith('.py') or file_path.endswith('.txt')):
            print(f"ProgrammingAgent: SECURITY ALERT - Attempt to modify unauthorized file type: {file_path}")
            self.error_logger.log_error(
                agent_name=self.name,
                error_message="Attempted to modify unauthorized file type",
                details={"file_path": file_path, "attempted_mode": mode}
            )
            # Notify SentinelAgent (placeholder)
            self.nexus_light.send_message("SentinelAgent", {
                "type": "security_alert",
                "details": f"ProgrammingAgent attempted to modify unauthorized file type: {file_path}"
            })
            return None

        print(f"ProgrammingAgent: Accessing system file: {file_path} in mode '{mode}'...")

        try:
            if mode == 'r':
                with open(absolute_file_path, 'r') as f:
                    file_content = f.read()
                print(f"ProgrammingAgent: Read content from {file_path}")
                return file_content
            elif mode == 'w':
                # Ensure directory exists before writing
                absolute_file_path.parent.mkdir(parents=True, exist_ok=True)
                with open(absolute_file_path, 'w') as f:
                    f.write(content if content is not None else "")
                print(f"ProgrammingAgent: Wrote content to {file_path}")
                return True
            elif mode == 'a': # Append mode for updates
                absolute_file_path.parent.mkdir(parents=True, exist_ok=True)
                with open(absolute_file_path, 'a') as f:
                    f.write(content if content is not None else "")
                print(f"ProgrammingAgent: Appended content to {file_path}")
                return True
            elif mode == 'd': # Delete mode
                if absolute_file_path.exists():
                    os.remove(absolute_file_path)
                    print(f"ProgrammingAgent: Deleted file {file_path}")
                    return True
                else:
                    print(f"ProgrammingAgent: File not found for deletion: {file_path}")
                    return False
            else:
                print(f"ProgrammingAgent: Unsupported file access mode: {mode}")
                return None
        except FileNotFoundError:
            print(f"ProgrammingAgent: File not found: {file_path}")
            return None
        except Exception as e:
            error_message = f"Error accessing file {file_path} in mode {mode}: {e}"
            self.error_logger.log_error(
                agent_name=self.name,
                error_message=error_message,
                details={"file_path": file_path, "mode": mode, "exception": str(e)}
            )
            return None

    def send_code_for_review(self, code_snippet: str, debugging_agent_name: str):
        """
        Simulates sending code to the Debugging/OptimizationAgent via NexusLight for review.
        """
        print(f"ProgrammingAgent: Sending code for review to {debugging_agent_name}...")
        self.nexus_light.send_message(
            recipient_agent_name=debugging_agent_name,
            message_payload={
                "type": "code_review_request",
                "code": code_snippet,
                "sender": self.name
            }
        )
        print(f"ProgrammingAgent: Code sent to {debugging_agent_name} for review.")

    def receive_feedback(self, feedback_data: dict):
        """
        Simulates receiving feedback from the Debugging/OptimizationAgent.
        """
        print(f"ProgrammingAgent: Received feedback from Debugging/OptimizationAgent: {feedback_data}")
        # Process feedback, e.g., update internal state, plan for code modifications
        if feedback_data.get("status") == "approved":
            print("ProgrammingAgent: Code approved by Debugging/OptimizationAgent.")
        elif feedback_data.get("status") == "revisions_needed":
            print(f"ProgrammingAgent: Revisions needed. Details: {feedback_data.get('details')}")
            # Trigger further actions based on feedback
        else:
            print("ProgrammingAgent: Unknown feedback status.")

    def query_rag_database(self, query: str):
        """
        Queries the agent's dedicated RAG database for information.
        """
        print(f"ProgrammingAgent: Querying RAG database for '{query}'...")
        if self.rag_database:
            return self.rag_database.retrieve_information(query)
        else:
            print("ProgrammingAgent: RAG database not initialized for this agent type.")
            return None