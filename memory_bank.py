# memory_bank.py

import os

class MemoryBank:
    """
    <PERSON><PERSON> reading from and writing to Markdown files for persistent context.
    """
    def __init__(self, base_dir: str = "memory-bank"):
        self.base_dir = base_dir
        os.makedirs(self.base_dir, exist_ok=True)
        print(f"Memory Bank initialized at: {os.path.abspath(self.base_dir)}") # Basic logging

    def read_file(self, filename: str) -> str:
        """Reads the content of a specific Markdown file."""
        filepath = os.path.join(self.base_dir, filename)
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            print(f"Read from {filepath}") # Basic logging
            return content
        except FileNotFoundError:
            print(f"File not found: {filepath}") # Basic logging
            return ""
        except Exception as e:
            print(f"Error reading {filepath}: {e}") # Basic logging
            return ""

    def write_file(self, filename: str, content: str):
        """Writes content to a specific Markdown file."""
        filepath = os.path.join(self.base_dir, filename)
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Wrote to {filepath}") # Basic logging
        except Exception as e:
            print(f"Error writing to {filepath}: {e}") # Basic logging

    def list_files(self) -> list[str]:
        """Lists all files in the memory bank directory."""
        try:
            files = [f for f in os.listdir(self.base_dir) if os.path.isfile(os.path.join(self.base_dir, f))]
            print(f"Listed files in {self.base_dir}") # Basic logging
            return files
        except Exception as e:
            print(f"Error listing files in {self.base_dir}: {e}") # Basic logging
            return []