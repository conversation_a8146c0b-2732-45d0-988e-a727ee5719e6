import os
import json
import datetime
import uuid

class ErrorLogger:
    """
    Handles logging of errors encountered by agents.
    Errors are stored in JSON format within a dedicated '/error' subfolder.
    """
    def __init__(self, base_dir=".", orchestrator_ref=None):
        """
        Initializes the ErrorLogger.

        Args:
            base_dir (str): The base directory from which the '/error' subfolder
                            path will be relative. Defaults to the current directory.
            orchestrator_ref: A reference to the Orchestrator to send errors to the GUI.
        """
        self.error_log_dir = os.path.join(base_dir, "error")
        os.makedirs(self.error_log_dir, exist_ok=True)
        self.orchestrator_ref = orchestrator_ref
        print(f"Error logs will be stored in: {self.error_log_dir}")

    def log_error(self, agent_name: str, error_message: str, details: dict = None):
        """
        Logs an error to a JSON file in the /error subfolder.

        Args:
            agent_name (str): The name of the agent that encountered the error.
            error_message (str): A concise description of the error.
            details (dict, optional): Additional details about the error,
                                      e.g., stack trace, context variables. Defaults to None.
        """
        timestamp = datetime.datetime.now().isoformat()
        error_entry = {
            "timestamp": timestamp,
            "agent_name": agent_name,
            "error_message": error_message,
            "details": details
        }

        # Generate a unique filename using timestamp and UUID
        filename = f"error_{timestamp.replace(':', '-').replace('.', '_')}_{uuid.uuid4()}.json"
        filepath = os.path.join(self.error_log_dir, filename)

        try:
            with open(filepath, 'w') as f:
                json.dump(error_entry, f, indent=4)
            print(f"Error logged successfully to {filepath}")
            if self.orchestrator_ref:
                self.orchestrator_ref.handle_error(agent_name, error_message, details)
        except IOError as e:
            print(f"Failed to write error log to {filepath}: {e}")

# Example Usage (for testing purposes, can be removed later)
if __name__ == "__main__":
    logger = ErrorLogger()
    logger.log_error(
        agent_name="TestAgent",
        error_message="Failed to process input data.",
        details={"input_id": "abc-123", "reason": "Invalid format", "traceback": "..."}
    )
    logger.log_error(
        agent_name="AnotherAgent",
        error_message="Database connection lost."
    )