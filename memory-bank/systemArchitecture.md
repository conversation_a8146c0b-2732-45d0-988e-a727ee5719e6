# System Architecture

## Core Principles
- **Self-Optimization:** Agents continuously seek to optimize performance and resource utilization.
- **Self-Upgrading:** Agents autonomously search for and test software upgrades.
- **Dynamic Scaling:** Agent count scales based on task requirements and system load.

## Scaling Rule
- For every 5 agents, 1 must be a dedicated Search Agent (e.g., 10 agents = 2 Search Agents).

## Orchestration
- The Orchestrator agent manages task assignment, project management, agent creation, and kill switches.
- Agents communicate via Nexus Light interfaces, with a Nexus Mind Light agent ensuring secure comms.

## Persistent Memory
- All agents read from and update the Memory Bank (markdown files) to maintain context across sessions ([Cline, 2025](https://cline.bot/blog/memory-bank-how-to-make-cline-an-ai-agent-that-never-forgets)).

## System Diagram
```mermaid
flowchart TD
    Orchestrator --> SearchAgent
    Orchestrator --> ProgrammingAgent
    Orchestrator --> DebuggingAgent
    Orchestrator --> CommunicationsAgent
    Orchestrator --> SecurityAgents
    Orchestrator --> SelfUpgradingAgents
``` 