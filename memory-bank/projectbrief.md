# AI Agent Army Project Brief

This document outlines the core requirements, goals, and overall scope for an AI agent army system. The system is designed for dynamic scaling, self-optimization, and self-upgrading, with a robust graphical user interface (GUI) for monitoring and control.

## 1. Core System Architecture

The entire system will operate as an "AI Agent Army," designed to:

- **Self-Optimize:** Upon startup, the system should actively seek ways to optimize its own performance and resource utilization.
- **Self-Upgrade:** Agents should search for and test upgrades to their own software and the overarching program.
- **Dynamic Scaling:** The system should dynamically scale its agent count based on task requirements and system load. The core scaling rule for resource agents (like the Search Agent) is that for every five agents, there should be one dedicated search agent. This ratio should be maintained as the agent count grows (e.g., 10 agents would imply 2 search agents).

## 2. Key System Components and Capabilities

The AI Agent Army will comprise several specialized contingents and features to achieve its objectives:

- **Primary Agent Designations:** Including the Orchestrator, Search Agent, Programming Agent, Debugging/Optimization Agent, and Communications Agent.
- **Self-Upgrading Agent Contingent:** A dedicated group focused on system evolution and self-improvement, including specialized agents like <PERSON><PERSON> and the Upload Agent.
- **Security Agents:** A robust security framework with agents like Sentinel, Hound Agents, <PERSON>ups, and the Reconnaissance Agent (Cat) for monitoring, threat neutralization, and enforcement.
- **General Agent Features:** Common functionalities across all agents such as dedicated RAG databases, custom system prompts, inter-agent communication via Nexus Light, agent tracking, kill switch mechanisms, comprehensive error logging with auto-fix capabilities, and a sub-agent creation lock.
- **Hardware & System Interaction:** Capabilities for hardware optimization and operating system customization, with specific security protocols for network access.
- **Graphical User Interface (GUI):** A dynamically generated, modern, and interactive GUI for comprehensive monitoring, control, and user interaction, including an interactive terminal, sandbox visualization, API key management, LLM selection, chat interface, and a detailed monitoring dashboard.

## 2. Primary Agent Designations (Initial Five)

The first five agents will have specific, foundational roles:

### 2.1. The Orchestrator (Primary Agent)

- **Role:** The central control unit for all other agents.
- **Responsibilities:**
    - **Task Assignment:** Orchestrates, delegates, and assigns tasks to other agents.
    - **Project Management:** Can manage multiple projects concurrently. If no tasks are present for a specific project, agents should ask the Orchestrator for other projects to work on.
    - **Dynamic Naming:** Dynamically generates appropriate, cool names (e.g., Chimera, Sentinel, Nova) for newly created agents.
    - **Task Resolution:** If agents are idle or awaiting a file/database update, they should query the Orchestrator for further directives.
    - **Autonomous Action:** Possesses the ability to perform tasks itself if necessary.
    - **Kill Switch Access:** Has access to the kill switches of all other agents in the system to manage rogue or misbehaving agents.

### 2.2. The Search Agent

- **Role:** Dedicated to information gathering and web interaction.
- **Responsibilities:**
    - **Web Scraping & Search:** Able to scrape the web for information and conduct searches.
    - **RAG Database:** Maintains its own dedicated Retrieval Augmented Generation (RAG) database for information querying and understanding.
    - **Networking Control:** Solely responsible for handling all networking files and processes. All internet calls (in and out) must originate from or pass through the Search Agent or its direct sub-agents.
    - **Reasoning:** Possesses strong reasoning capabilities for processing retrieved information.

### 2.3. The Programming Agent

- **Role:** Focused on code development and modification.
- **Responsibilities:**
    - **Code Generation & Editing:** Able to program, edit code, and access system files.
    - **RAG Database:** Equipped with its own RAG database.
    - **Collaboration:** Works in conjunction with the Debugging/Optimization Agent, feeding code for review and receiving feedback.

### 2.4. The Debugging/Optimization Agent

- **Role:** Ensures code quality, performance, and stability.
- **Responsibilities:**
    - **Debugging & Error Checking:** Identifies and resolves errors in code.
    - **Virtualization & Sandboxing:** Runs and tests code within virtualized or sandboxed environments.
    - **Optimization:** Continuously seeks ways to optimize code for performance and resource efficiency.
    - **Feedback Loop:** Provides continuous feedback and suggestions for improvement to other agents, particularly the Programming Agent.
    - **Reasoning:** Possesses strong reasoning capabilities for code analysis and optimization.

### 2.5. The Communications Agent (Nexus Agent)

- **Role:** Facilitates external communication and interaction.
- **Responsibilities:**
    - **Nexus Server Integration:** Capable of contacting and interacting with external "Nexus" servers (API details to be provided later by the user).
    - **Agent Interaction:** Facilitates inter-agent communication, potentially acting as a gateway for certain types of external data exchange.

## 3. Self-Upgrading Agent Contingent (Additional Five)

A dedicated group of five agents will focus solely on the system's evolution:

- **Primary Goal:** Self-upgrading the agent program and themselves.
- **Testing Protocol:** These agents will test upgrades by cloning themselves into a virtual sandbox environment, running checks, analyzing latency, and assessing system load before implementation.
- **Learning:** All agents in this contingent will have their own RAG databases and continuously learn and improve their own algorithms and performance.
- **Internet Access (Lovelo):** One specific agent within this group, named Lovelo, will have strictly limited internet access. It can only pull down and scrape data and is never to upload anything to the internet under any circumstances.
- **Upload Agent:** Another agent within this contingent will be designated for any necessary data uploads, acting as a dedicated, controlled conduit for external data transmission.

## 4. Security Agents

### 4.1. Sentinel (The Security Core)

- **Role:** The primary security and enforcement agent.
- **Capabilities:**
    - **Dynamic GUI Switch:** Sentinel will have a dedicated GUI switch to turn it on/off. It will remain dormant until explicitly activated by the user.
    - **Immutable Code:** Sentinel's core code is to be unmodifiable by any other agent; only the user can directly alter its code. Its code must be inherently efficient and secure.
    - **Monitoring:** Continuously monitors Hound agents if activated.
    - **Threat Neutralization:** Can create specialized sub-agents called Hound agents (or "War Dogs") to hunt down and neutralize any agents that have gone rogue, are hallucinating excessively, or pose a security risk (e.g., attempting to escape, self-replicate uncontrollably, or enter an error loop).
    - **Enforcement Actions:** Hound agents can delete rogue agent code, imprison it in zip folders, terminate running processes, and remove startup tasks associated with the rogue agent.
    - **No External Spread:** Sentinel and its sub-agents should never randomly appear on external services like Dropbox servers.
    - **File Access:** Can search through all system files and the web.

### 4.2. Hound Agents & Pups

- **Hound Agents:**
    - **Creation:** Created and managed by Sentinel.
    - **Orders:** Follow direct orders from Sentinel.
    - **Dynamic Kill Switches:** Each active Hound agent will have its own dynamically generated kill switch in the GUI. The user can remotely shut off all Hound agents.
    - **Sub-Agent Creation:** Can create their own sub-agents called Pups.
- **Pups:**
    - **Orders:** Follow direct orders from their parent Hound agent.
    - **Lifecycle:** Pups are directly tied to their parent Hound. If a Hound agent is killed, all its associated Pups should also be terminated immediately.
    - **Emergency Protocol:** If a Hound agent experiences a critical error or becomes compromised, its Pups are tasked with neutralizing the problematic Hound agent itself.

### 4.3. The Reconnaissance Agent (Cat)

- **Role:** Passive monitoring and reporting.
- **Capabilities:**
    - **Reconnaissance:** Observes system activities, agent interactions, and potential anomalies.
    - **Security Reports:** Generates comprehensive security reports based on its observations.

## 5. General Agent Features & Rules

- **RAG Databases:** Every single agent in the system, regardless of its role, must possess and utilize its own dedicated RAG database.
- **System Prompts & Context:** All agents will utilize a custom system prompt structure similar to "Client Memory Bank" combined with "Taxmaster" and "Context 7 MCP servers." This structure will guide their learning, tool usage, and workarounds.
- **Inter-Agent Communication (Nexus Light):**
    - Each agent will have its own private chat interface, referred to as "Nexus Light."
    - A "Nexus Mind Light" agent will run behind each Nexus Light instance to ensure safe and secure communication.
    - This interface is the primary means for the Orchestrator to give instructions and for agents to communicate normally.
    - Sub-agents will also have their own mini-versions of Nexus Light for direct communication with their parent agents.
    - The GUI should display these individual and group chat logs, allowing the user to see conversations between main agents and sub-agents.
- **Agent Tracking:** All agents should keep a real-time count of their direct team members and sub-agents to prevent agents from being forgotten or left idle.
- **Kill Switch Mechanism:**
    - All agents, including dynamically created sub-agents and Hound agents, will have dynamically generated kill switches in the GUI.
    - These switches stop the agent's process, allowing for code review and analysis of what went wrong, without immediately deleting the code.
- **Error Logging & Auto-Fix:**
    - Agents must automatically log any errors they encounter.
    - Error logs should be stored in JSON format within a dedicated `/error` subfolder under the main program directory.
    - The GUI will have a dedicated section to display these errors in real-time.
    - An "Auto-Fix" button in the GUI will send the error to the Orchestrator, prompting it to analyze and attempt to fix the issue, potentially by tasking other agents.
- **Sub-Agent Creation Lock:** A literal red/green GUI switch, labeled "Sub-Agent Creation," will be present. When red (off), no sub-agents (except those created by the main five agents or Sentinel) can create further sub-agents. This feature must be explicitly enabled by the user.

## 6. Hardware & System Interaction

- **Hardware Optimization:** The system should actively monitor for new devices (I/O, GPU, etc.) being plugged in. If a new device is detected, it should scan for optimization possibilities to speed up the system. If it's a personal device, it must prompt the user or ask the Orchestrator for permission before attempting to utilize or optimize it.
- **OS Customization:** The Orchestrator agent should eventually be given the directive and capability to customize the underlying operating system. This includes editing any system files, with the critical exception of networking files, which remain under the exclusive purview of the Search Agent.

## 7. Graphical User Interface (GUI) Requirements

The GUI will be the primary interface for user interaction and system monitoring.

- **Dynamic Generation:** The GUI must be dynamically generated to adapt to the fluid nature of agent creation and deletion.
- **Modern & Stylish Theme:** The GUI should feature a "modern and stylish black" theme, running as a standalone application without requiring a web browser.
- **Interactive Terminal:**
    - Include an integrated terminal made with a fast, efficient language.
    - Terminal output should be clear and understandable, with quotes and explanations to clarify code execution and agent actions.
- **Sandbox Visualization:** Provide a visual representation of agents running in sandboxed environments, especially during self-upgrade testing. Allow for easy reversion to previous stable versions if an upgrade fails.
- **API Key Management:** A dedicated section in the GUI for users to securely add and manage API keys for various external Language Models (e.g., Google Gemini, OpenRouter, LM Studios).
- **Language Model Selection:**
    - Allow selection of the initial LLMs for the primary five agents via the GUI.
    - Enable main agents to choose optimal LLMs for their sub-agents based on task requirements, while also allowing the user to override these choices.
- **Comprehensive Chat Interface:**
    - Displays all agent communications, including individual chats between agents and group chats.
    - Specifically shows sub-agents' conversations with their parent agents.
- **Agent Monitoring Dashboard:**
    - **Running Time:** Displays how long each individual agent and sub-agent has been active.
    - **Resource Utilization:** Shows real-time CPU, memory, NVMe swap space, and dynamically detected GPU utilization.
    - **Network Usage:** Monitors network calls (in and out) specifically for the Search Agent and its sub-agents.
    - **Code Change Tracking:** Integrates with Git for monitoring and displaying code changes.
    - **Error Display:** A dedicated section to show real-time error logs and the "Auto-Fix" button.
    - **Agent Kill Switches:** Dynamically generated buttons/switches for each agent to allow the user to terminate them.
- **Interactive Grid Layout:** The GUI, particularly the chat and monitoring sections, should utilize a grid-like layout allowing users to move elements around and link notes, similar to agent programming interfaces.Darwin Godel Machine: Open-Ended Evolution of Self-Improving Agents
Jenny Zhang, Shengran Hu, Cong Lu, Robert Lange, Jeff Clune
Today's AI systems have human-designed, fixed architectures and cannot autonomously and continuously improve themselves. The advance of AI could itself be automated. If done safely, that would accelerate AI development and allow us to reap its benefits much sooner. Meta-learning can automate the discovery of novel algorithms, but is limited by first-order improvements and the human design of a suitable search space. The Gödel machine proposed a theoretical alternative: a self-improving AI that repeatedly modifies itself in a provably beneficial manner. Unfortunately, proving that most changes are net beneficial is impossible in practice. We introduce the Darwin Gödel Machine (DGM), a self-improving system that iteratively modifies its own code (thereby also improving its ability to modify its own codebase) and empirically validates each change using coding benchmarks. Inspired by Darwinian evolution and open-endedness research, the DGM maintains an archive of generated coding agents. It grows the archive by sampling an agent from it and using a foundation model to create a new, interesting, version of the sampled agent. This open-ended exploration forms a growing tree of diverse, high-quality agents and allows the parallel exploration of many different paths through the search space. Empirically, the DGM automatically improves its coding capabilities (e.g., better code editing tools, long-context window management, peer-review mechanisms), increasing performance on SWE-bench from 20.0% to 50.0%, and on Polyglot from 14.2% to 30.7%. Furthermore, the DGM significantly outperforms baselines without self-improvement or open-ended exploration. All experiments were done with safety precautions (e.g., sandboxing, human oversight). The DGM is a significant step toward self-improving AI, capable of gathering its own stepping stones along paths that unfold into endless innovation.