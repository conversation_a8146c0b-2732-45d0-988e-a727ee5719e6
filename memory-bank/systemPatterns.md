# AI Agent Army System Patterns

This document details the system architecture, key technical decisions, design patterns, component relationships, and critical implementation paths for the AI Agent Army project.

## 1. Core System Architecture

The entire system operates as an "AI Agent Army," designed for:

- **Self-Optimization:** The system actively seeks ways to optimize its own performance and resource utilization upon startup. This implies a continuous monitoring and adjustment mechanism.
- **Self-Upgrading:** Agents are designed to search for and test upgrades to their own software and the overarching program. This requires a robust testing protocol within sandboxed environments.
- **Dynamic Scaling:** The system dynamically scales its agent count based on task requirements and system load. A core scaling rule for resource agents (like the Search Agent) is that for every five agents, there should be one dedicated search agent. This ratio must be maintained as the agent count grows.

## 2. Primary Agent Designations (Initial Five) and Relationships

These agents form the foundational operational units of the system:

- **The Orchestrator (Primary Agent):**
    - **Role:** Central control unit for all other agents.
    - **Key Patterns:** Task assignment, project management (concurrently), dynamic agent naming, task resolution for idle agents, autonomous action capability, and kill switch access for all agents.
- **The Search Agent:**
    - **Role:** Dedicated to information gathering and web interaction.
    - **Key Patterns:** Web scraping, search capabilities, maintenance of a dedicated Retrieval Augmented Generation (RAG) database, sole responsibility for all networking files and processes (all internet calls must originate from or pass through it), and strong reasoning capabilities for processing retrieved information.
- **The Programming Agent:**
    - **Role:** Focused on code development and modification.
    - **Key Patterns:** Code generation, editing, access to system files, equipped with its own RAG database, and collaboration with the Debugging/Optimization Agent (feeding code for review and receiving feedback).
- **The Debugging/Optimization Agent:**
    - **Role:** Ensures code quality, performance, and stability.
    - **Key Patterns:** Debugging and error checking, running and testing code within virtualized or sandboxed environments, continuous optimization for performance and resource efficiency, providing continuous feedback and suggestions to other agents (especially the Programming Agent), and strong reasoning capabilities for code analysis and optimization.
- **The Communications Agent (Nexus Agent):**
    - **Role:** Facilitates external communication and interaction.
    - **Key Patterns:** Capable of contacting and interacting with external "Nexus" servers, and facilitating inter-agent communication (potentially acting as a gateway for external data exchange).

## 3. Self-Upgrading Agent Contingent (Additional Five)

A dedicated group of five agents focuses solely on the system's evolution:

- **Primary Goal:** Self-upgrading the agent program and themselves.
- **Testing Protocol:** These agents test upgrades by cloning themselves into a virtual sandbox environment, running checks, analyzing latency, and assessing system load before implementation. This is a critical pattern for safe and robust upgrades.
- **Learning:** All agents in this contingent will have their own RAG databases and continuously learn and improve their own algorithms and performance.
- **Internet Access (Lovelo):** One specific agent within this group, named Lovelo, will have strictly limited internet access. It can only pull down and scrape data and is never to upload anything to the internet under any circumstances. This is a key security and data flow pattern.
- **Upload Agent:** Another agent within this contingent will be designated for any necessary data uploads, acting as a dedicated, controlled conduit for external data transmission. This enforces a controlled upload pattern.

## 4. Security Agents

These agents implement the system's security and enforcement patterns:

- **Sentinel (The Security Core):**
    - **Role:** The primary security and enforcement agent.
    - **Key Patterns:** Dynamic GUI switch (dormant until activated), immutable core code (only user can alter), continuous monitoring of Hound agents (if activated), threat neutralization via specialized sub-agents (Hound agents), enforcement actions (code deletion, zipping, process termination, startup task removal), strict pattern of no external spread (e.g., Dropbox), and ability to search all system files and the web.
- **Hound Agents & Pups:**
    - **Hound Agents:** Created and managed by Sentinel, follow direct orders from Sentinel, have dynamic GUI kill switches, and can create their own sub-agents called Pups.
    - **Pups:** Follow direct orders from their parent Hound agent, their lifecycle is directly tied to their parent Hound (termination upon parent's death), and an emergency protocol tasks them with neutralizing a problematic parent Hound. These define a hierarchical security enforcement pattern.
- **The Reconnaissance Agent (Cat):**
    - **Role:** Passive monitoring and reporting.
    - **Key Patterns:** Observes system activities, agent interactions, and potential anomalies, and generates comprehensive security reports.

## 5. General Agent Features & Rules (Design Patterns & Mechanisms)

These are overarching design patterns applicable to most or all agents:

- **RAG Databases:** All main agents (the initial five, self-upgrading contingent, and security agents) must possess and utilize their own dedicated Retrieval Augmented Generation (RAG) database. This is a fundamental memory and context pattern for main agents.
- **Sub-Agent Context Management:** To save resources, sub-agents will NOT have individual RAG databases. Instead, they will rely on a custom version of Context 7 MCP server for their context and memory management. This is a key technical decision for resource optimization.
- **System Prompts & Context:** All agents (main and sub-agents) will utilize a custom system prompt structure that integrates the principles of a custom version of Cline Memory Bank, a custom version of the Taskmaster MCP server, and "Context 7 MCP servers." This pattern guides their learning, tool usage, and workarounds.
- **Inter-Agent Communication (Nexus Light):**
    - Each agent will have its own private chat interface, referred to as "Nexus Light."
    - A "Nexus Mind Light" agent will run behind each Nexus Light instance to ensure safe and secure communication.
    - This interface is the primary means for the Orchestrator to give instructions and for agents to communicate normally.
    - Sub-agents will also have their own mini-versions of Nexus Light for direct communication with their parent agents.
    - The GUI should display these individual and group chat logs, allowing the user to see conversations between main agents and sub-agents. This defines the communication and observability pattern.
- **Agent Tracking:** All agents should keep a real-time count of their direct team members and sub-agents to prevent agents from being forgotten or left idle. This is a management and resource utilization pattern.
- **Kill Switch Mechanism:** All agents, including dynamically created sub-agents and Hound agents, will have dynamically generated kill switches in the GUI. These switches stop the agent's process, allowing for code review and analysis of what went wrong, without immediately deleting the code. This is a critical control and debugging pattern.
- **Error Logging & Auto-Fix:** Agents must automatically log any errors they encounter. Error logs should be stored in JSON format within a dedicated `/error` subfolder under the main program directory. The GUI will have a dedicated section to display these errors in real-time. An "Auto-Fix" button in the GUI will send the error to the Orchestrator, prompting it to analyze and attempt to fix the issue, potentially by tasking other agents. This defines the error handling and recovery pattern.
- **Sub-Agent Creation Lock:** A literal red/green GUI switch, labeled "Sub-Agent Creation," will be present. When red (off), no sub-agents (except those created by the main five agents or Sentinel) can create further sub-agents. This feature must be explicitly enabled by the user. This is a control and security pattern.

## 6. Hardware & System Interaction

These patterns define how the system interacts with its environment:

- **Hardware Optimization:** The system should actively monitor for new devices (I/O, GPU, etc.) being plugged in. If a new device is detected, it should scan for optimization possibilities to speed up the system. If it's a personal device, it must prompt the user or ask the Orchestrator for permission before attempting to utilize or optimize it. This is a resource management and user consent pattern.
- **OS Customization:** The Orchestrator agent should eventually be given the directive and capability to customize the underlying operating system. This includes editing any system files, with the critical exception of networking files, which remain under the exclusive purview of the Search Agent. This defines the system control and security boundary patterns.