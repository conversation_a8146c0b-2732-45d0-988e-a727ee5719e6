# AI Agent Army: General Agent Features & Rules

This document outlines the overarching features and rules that apply to most or all agents within the "AI Agent Army" system, defining common functionalities and operational patterns.

## 5. General Agent Features & Rules

- **RAG Databases:** All main agents (the initial five, self-upgrading contingent, and security agents) must possess and utilize their own dedicated Retrieval Augmented Generation (RAG) database. This ensures persistent memory and context for these agents.
- **Sub-Agent Context Management:** To save resources, sub-agents will NOT have individual RAG databases. Instead, they will rely on a custom version of Context 7 MCP server for their context and memory management.
- **System Prompts & Context:** All agents (main and sub-agents) will utilize a custom system prompt structure that integrates the principles of a custom version of Cline Memory Bank, a custom version of the Taskmaster MCP server, and "Context 7 MCP servers." This structure will guide their learning, tool usage, and workarounds, ensuring consistent behavior and adherence to project guidelines.
- **Inter-Agent Communication (Nexus Light):**
    - Each agent will have its own private chat interface, referred to as "Nexus Light."
    - A "Nexus Mind Light" agent will run behind each Nexus Light instance to ensure safe and secure communication.
    - This interface is the primary means for the Orchestra<PERSON> to give instructions and for agents to communicate normally.
    - Sub-agents will also have their own mini-versions of Nexus Light for direct communication with their parent agents.
    - The GUI should display these individual and group chat logs, allowing the user to see conversations between main agents and sub-agents, providing transparency and monitoring capabilities.
- **Agent Tracking:** All agents should keep a real-time count of their direct team members and sub-agents to prevent agents from being forgotten or left idle. This ensures efficient resource management and oversight.
- **Kill Switch Mechanism:**
    - All agents, including dynamically created sub-agents and Hound agents, will have dynamically generated kill switches in the GUI.
    - These switches stop the agent's process, allowing for code review and analysis of what went wrong, without immediately deleting the code. This provides a critical control and debugging mechanism.
- **Error Logging & Auto-Fix:**
    - Agents must automatically log any errors they encounter.
    - Error logs should be stored in JSON format within a dedicated `/error` subfolder under the main program directory.
    - The GUI will have a dedicated section to display these errors in real-time.
    - An "Auto-Fix" button in the GUI will send the error to the Orchestrator, prompting it to analyze and attempt to fix the issue, potentially by tasking other agents. This establishes a robust error handling and recovery system.
- **Sub-Agent Creation Lock:** A literal red/green GUI switch, labeled "Sub-Agent Creation," will be present. When red (off), no sub-agents (except those created by the main five agents or Sentinel) can create further sub-agents. This feature must be explicitly enabled by the user, providing a critical security and control measure over agent proliferation.