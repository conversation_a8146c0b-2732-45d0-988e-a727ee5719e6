# AI Agent Army Deployment Guide

This document outlines the conceptual steps and considerations for preparing and deploying the AI Agent Army system.

## Packaging

The AI Agent Army system consists of a Python backend and an Electron frontend. Packaging involves bundling both components for distribution:

1.  **Package the Python Backend:**
    *   Use tools like PyInstaller or cx_Freeze to create a standalone executable of the Python backend, including all its dependencies. This ensures the backend can run without a system-wide Python installation.
2.  **Package the Electron Frontend:**
    *   Use Electron packaging tools such as `electron-builder` or `electron-packager`.
    *   These tools will bundle the Electron application code (HTML, CSS, JavaScript) and the previously created Python backend executable and its necessary files into a single distributable package (e.g., `.exe` for Windows, `.dmg` for macOS, `.AppImage` or `.deb` for Linux).
    *   This combined package provides a single artifact for users to install.
3.  **Consider Containerization:**
    *   For more controlled environments or server deployments, containerization (e.g., Docker) can still be used to package the entire application (Electron frontend and Python backend) and its environment into a portable unit.

## Installation Procedures

To install the AI Agent Army system, users would typically install the bundled application package created during the packaging phase.

1.  **Download and Run Installer:**
    *   Obtain the appropriate application package for your operating system (e.g., `.exe`, `.dmg`, `.AppImage`).
    *   Run the installer or executable. This will typically place the application files in the appropriate system location.

2.  **Prerequisites (if not bundled):**
    *   While Electron packaging aims for standalone distribution, verify if any external dependencies are required (e.g., specific system libraries). Node.js is usually bundled within the Electron package.

3.  **Running the Application:**
    *   Launch the installed application like any other desktop application on your system.
    *   The Electron frontend will start and automatically manage the lifecycle of the bundled Python backend process.

4.  **Initial Setup Steps:**
    *   **Configuration Files:** Users might need to create or modify configuration files (e.g., for API keys, database connections). These might be managed via the GUI or external files.
    *   **Environment Variables:** Set up necessary environment variables for secure API key storage or other system-wide settings.
    *   **Database Initialization:** Initialize RAG databases (e.g., Qdrant) if they are not pre-populated or require initial setup. This might be handled by the Python backend on first run.

## Deployment Guidelines

Deploying the AI Agent Army system, with its Python backend and Electron frontend, involves considerations for various environments, from local machines to cloud servers.

### General Considerations:

*   **Dual Process Management:** The deployment must ensure both the Electron frontend process and the Python backend process are running and can communicate effectively. The Electron application is typically responsible for launching and managing the Python backend process.
*   **Inter-Process Communication (IPC):** Consider the mechanism used for communication between the Electron frontend (JavaScript) and the Python backend. This could involve local HTTP/WebSocket servers run by the Python backend, or standard IPC methods provided by Electron and Python.
*   **Cross-Platform Compatibility:** While Electron provides cross-platform capabilities for the GUI, ensure the Python backend and its dependencies are also compatible with all target operating systems. Packaging tools help manage this, but underlying system dependencies must be considered.

*   **Resource Allocation (CPU, RAM, GPU):**
    *   **CPU:** Agents are computationally intensive; allocate sufficient CPU cores.
    *   **RAM:** Each agent, especially those with dedicated RAG databases, will consume significant memory. Monitor and allocate RAM based on the number of active agents and the size of their RAG databases.
    *   **GPU:** If LLM inference or other computationally heavy tasks are offloaded to a GPU, ensure a compatible GPU with adequate VRAM is available and properly configured.
*   **Networking Setup:**
    *   **Firewall Rules:** Configure firewall rules to allow necessary inbound and outbound connections, especially for communication with external LLMs and Nexus servers.
    *   **Port Forwarding:** If external Nexus servers are used or the system needs to be accessible from outside the local network, set up appropriate port forwarding.
    *   **Search Agent's Networking Control:** Remember the architectural constraint that all internet calls must originate from or pass through the Search Agent. Ensure network configurations respect this.
*   **Security Best Practices:**
    *   **API Key Storage:** Implement secure methods for storing API keys (e.g., environment variables, secret management services) rather than hardcoding them.
    *   **Access Control:** Restrict access to deployment environments and sensitive system files.
    *   **Principle of Least Privilege:** Ensure the application and its components run with the minimum necessary permissions.
    *   **Network Egress Filtering:** Especially for agents like Lovelo, implement strict network egress filtering to prevent unauthorized data uploads.
*   **Monitoring and Logging Setup:**
    *   **Real-time Monitoring:** Integrate tools for real-time monitoring of CPU, RAM, GPU, and network utilization.
    *   **Centralized Logging:** Implement a centralized logging solution for error logs (stored in JSON format in the `/error` subfolder) and other system events to facilitate debugging and auditing in a production environment.
    *   **Alerting:** Set up alerts for critical errors or resource thresholds.
*   **Backup and Recovery Strategies:**
    *   **Data Backup:** Regularly back up critical data, including RAG databases, configuration files, and error logs.
    *   **System Snapshots:** Consider taking system snapshots of the deployed environment for quick recovery in case of failure.
    *   **Disaster Recovery Plan:** Develop a plan for recovering the system in the event of a major outage or data loss.