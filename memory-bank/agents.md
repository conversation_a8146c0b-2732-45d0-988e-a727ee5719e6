# AI Agent Army: Agent Designations and Roles

This document details the specific roles, responsibilities, and key characteristics of the various AI agents within the "AI Agent Army" system.

## 2. Primary Agent Designations (Initial Five)

These agents form the foundational operational units of the system:

### 2.1. The Orchestrator (Primary Agent)
- **Role:** The central control unit for all other agents.
- **Responsibilities:**
    - **Task Assignment:** Orchestrates, delegates, and assigns tasks to other agents.
    - **Project Management:** Can manage multiple projects concurrently. If no tasks are present for a specific project, agents should ask the Orchestrator for other projects to work on.
    - **Dynamic Naming:** Dynamically generates appropriate, cool names (e.g., Chimera, Sentinel, Nova) for newly created agents.
    - **Task Resolution:** If agents are idle or awaiting a file/database update, they should query the Orchestrator for further directives.
    - **Autonomous Action:** Possesses the ability to perform tasks itself if necessary.
    - **Kill Switch Access:** Has access to the kill switches of all other agents in the system to manage rogue or misbehaving agents.

### 2.2. The Search Agent
- **Role:** Dedicated to information gathering and web interaction.
- **Responsibilities:**
    - **Web Scraping & Search:** Able to scrape the web for information and conduct searches.
    - **RAG Database:** Maintains its own dedicated Retrieval Augmented Generation (RAG) database for information querying and understanding.
    - **Networking Control:** Solely responsible for handling all networking files and processes. All internet calls (in and out) must originate from or pass through the Search Agent or its direct sub-agents.
    - **Reasoning:** Possesses strong reasoning capabilities for processing retrieved information.

### 2.3. The Programming Agent
- **Role:** Focused on code development and modification.
- **Responsibilities:**
    - **Code Generation & Editing:** Able to program, edit code, and access system files.
    - **RAG Database:** Equipped with its own dedicated RAG database.
    - **Collaboration:** Works in conjunction with the Debugging/Optimization Agent, feeding code for review and receiving feedback.

### 2.4. The Debugging/Optimization Agent
- **Role:** Ensures code quality, performance, and stability.
- **Responsibilities:**
    - **Debugging & Error Checking:** Identifies and resolves errors in code.
    - **Virtualization & Sandboxing:** Runs and tests code within virtualized or sandboxed environments.
    - **Optimization:** Continuously seeks ways to optimize code for performance and resource efficiency.
    - **Feedback Loop:** Provides continuous feedback and suggestions for improvement to other agents, particularly the Programming Agent.
    - **Reasoning:** Possesses strong reasoning capabilities for code analysis and optimization.

### 2.5. The Communications Agent (Nexus Agent)
- **Role:** Facilitates external communication and interaction.
- **Responsibilities:**
    - **Nexus Server Integration:** Capable of contacting and interacting with external "Nexus" servers (API details to be provided later by the user).
    - **Agent Interaction:** Facilitates inter-agent communication, potentially acting as a gateway for certain types of external data exchange.

## 3. Self-Upgrading Agent Contingent (Additional Five)

A dedicated group of five agents will focus solely on the system's evolution:

- **Primary Goal:** Self-upgrading the agent program and themselves.
- **Testing Protocol:** These agents will test upgrades by cloning themselves into a virtual sandbox environment, running checks, analyzing latency, and assessing system load before implementation.
- **Learning:** All agents in this contingent will have their own dedicated RAG databases and continuously learn and improve their own algorithms and performance.
- **Internet Access (Lovelo):** One specific agent within this group, named Lovelo, will have strictly limited internet access. It can only pull down and scrape data and is never to upload anything to the internet under any circumstances.
- **Upload Agent:** Another agent within this contingent will be designated for any necessary data uploads, acting as a dedicated, controlled conduit for external data transmission.

## 4. Security Agents

These agents implement the system's security and enforcement:

### 4.1. Sentinel (The Security Core)
- **Role:** The primary security and enforcement agent.
- **Capabilities:**
    - **Dynamic GUI Switch:** Sentinel will have a dedicated GUI switch to turn it on/off. It will remain dormant until explicitly activated by the user.
    - **Immutable Code:** Sentinel's core code is to be unmodifiable by any other agent; only the user can directly alter its code. Its code must be inherently efficient and secure.
    - **Monitoring:** Continuously monitors Hound agents if activated.
    - **Threat Neutralization:** Can create specialized sub-agents called Hound agents (or "War Dogs") to hunt down and neutralize any agents that have gone rogue, are hallucinating excessively, or pose a security risk (e.g., attempting to escape, self-replicate uncontrollably, or enter an error loop).
    - **Enforcement Actions:** Hound agents can delete rogue agent code, imprison it in zip folders, terminate running processes, and remove startup tasks associated with the rogue agent.
    - **No External Spread:** Sentinel and its sub-agents should never randomly appear on external services like Dropbox servers.
    - **File Access:** Can search through all system files and the web.

### 4.2. Hound Agents & Pups
- **Hound Agents:**
    - **Creation:** Created and managed by Sentinel.
    - **Orders:** Follow direct orders from Sentinel.
    - **Context Management:** Do not have individual RAG databases. Instead, they use the custom version of Context 7 MCP server for context management.
    - **Dynamic Kill Switches:** Each active Hound agent will have its own dynamically generated kill switch in the GUI. The user can remotely shut off all Hound agents.
    - **Sub-Agent Creation:** Can create their own sub-agents called Pups.
- **Pups:**
    - **Orders:** Follow direct orders from their parent Hound agent.
    - **Context Management:** Will utilize a custom version of Context 7 MCP server for their context and memory management.
    - **Lifecycle:** Pups are directly tied to their parent Hound. If a Hound agent is killed, all its associated Pups should also be terminated immediately.
    - **Emergency Protocol:** If a Hound agent experiences a critical error or becomes compromised, its Pups are tasked with neutralizing the problematic Hound agent itself.

### 4.3. The Reconnaissance Agent (Cat)
- **Role:** Passive monitoring and reporting.
- **Capabilities:**
    - **Reconnaissance:** Observes system activities, agent interactions, and potential anomalies.
    - **Security Reports:** Generates comprehensive security reports based on its observations.