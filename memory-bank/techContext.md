# AI Agent Army Technical Context

This document outlines the technologies, technical constraints, dependencies, and development considerations for the AI Agent Army project.

## Technologies and Concepts

- **Retrieval Augmented Generation (RAG) Databases:** A core technology requirement. Every single agent in the system must possess and utilize its own dedicated RAG database for information querying and understanding.
- **Virtualization & Sandboxing:** Essential for the Debugging/Optimization Agent to run and test code safely, and for the Self-Upgrading Agent Contingent to test upgrades by cloning agents into a virtual sandbox environment.
- **Git Integration:** The GUI's Agent Monitoring Dashboard requires integration with Git for monitoring and displaying code changes, implying the use of Git for version control.
- **Graphical User Interface (GUI) Framework:** The GUI is specified as a standalone application with a "modern and stylish black" theme and an integrated terminal. **Electron** is a strong candidate for this, allowing for cross-platform desktop applications using web technologies. The integrated terminal should be made with a fast, efficient language.
- **JSON:** Error logs must be automatically logged and stored in JSON format within a dedicated `/error` subfolder under the main program directory.
- **Language Models (LLMs):** The system will integrate with various external LLMs (e.g., Google Gemini, OpenRouter, LM Studios). This implies the need for API clients and secure API key management.
- **Qdrant:** A vector similarity search engine, ideal for implementing the Retrieval Augmented Generation (RAG) databases required for every agent.
- **QEMU/KVM:** For virtualization and sandboxing, essential for the Debugging/Optimization Agent and the Self-Upgrading Agent Contingent to run and test code safely in isolated environments.
- **ZeroMQ:** A high-performance asynchronous messaging library, suitable for efficient and flexible inter-agent communication (Nexus Light) and potentially for communication with external Nexus servers.
- **psutil:** A cross-platform library for retrieving information on running processes and system utilization (CPU, memory, disks, network, sensors). This will be crucial for the Agent Monitoring Dashboard's resource utilization display.

## Technical Constraints

- **Sentinel's Immutable Code:** The core code of the Sentinel agent is explicitly stated to be unmodifiable by any other agent; only the user can directly alter its code. Its code must be inherently efficient and secure, suggesting careful design and potentially a separate deployment or protection mechanism.
- **Search Agent's Networking Control:** The Search Agent is solely responsible for handling all networking files and processes. All internet calls (in and out) must originate from or pass through the Search Agent or its direct sub-agents. This is a critical security and architectural constraint, limiting network access for other agents. The Orchestrator agent is explicitly restricted from editing networking files even when customizing the OS.
- **Lovelo's Internet Access:** The Lovelo agent within the Self-Upgrading Contingent has strictly limited internet access. It can only pull down and scrape data and is never to upload anything to the internet under any circumstances. This requires strict network egress filtering for this specific agent.
- **Sub-Agent Creation Lock:** A literal red/green GUI switch, labeled "Sub-Agent Creation," will be present. When red (off), no sub-agents (except those created by the main five agents or Sentinel) can create further sub-agents. This implies a programmatic control mechanism for agent creation.
- **No External Spread:** Sentinel and its sub-agents should never randomly appear on external services like Dropbox servers. This is a security and deployment constraint.

## Dependencies

- **External Language Models (LLMs):** Integration with various external LLMs (e.g., Google Gemini, OpenRouter, LM Studios) is a key dependency.
- **Nexus Servers:** The Communications Agent (Nexus Agent) is capable of contacting and interacting with external "Nexus" servers. API details for these servers are to be provided later by the user.
- **Operating System (OS) Interaction:** The system needs capabilities to monitor for new devices, scan for optimization possibilities, and the Orchestrator needs to customize the underlying OS (excluding networking files). This implies OS-level API interactions or system calls.

## Development Setup and Considerations

- **Error Logging Location:** A dedicated `/error` subfolder under the main program directory is required for storing JSON error logs. This impacts file system structure and error handling implementation.
- **System Prompt Structure:** Agents will utilize a custom system prompt structure similar to "Client Memory Bank" combined with "Taxmaster" and "Context 7 MCP servers." This structure needs to be defined, implemented, and consistently applied across all agents.
- **Inter-Agent Communication Mechanism:** The "Nexus Light" private chat interface and the "Nexus Mind Light" security agent need to be designed and implemented. This will involve defining communication protocols and security measures.
- **Dynamic GUI Generation:** The GUI must be designed to be dynamically generated to adapt to agent creation and deletion. This suggests a component-based or reactive UI framework.
- **Resource Monitoring:** Implementation is needed to monitor and display real-time CPU, memory, NVMe swap space, and dynamically detected GPU utilization. This requires system-level resource monitoring capabilities.
- **Hardware Monitoring:** The system needs to actively monitor for new devices being plugged in and scan for optimization possibilities, requiring user/Orchestrator permission for personal devices. This involves hardware detection and interaction.
- **Security Best Practices:** Given the nature of the agents (self-upgrading, OS customization, network access), robust security practices must be integrated throughout the development process, including secure coding, access control, and threat modeling.