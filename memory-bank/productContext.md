# AI Agent Army Product Context

This document describes the purpose, high-level functionality, and user experience goals for the AI Agent Army project.

## Purpose and High-Level Functionality

The core purpose of the AI Agent Army is to create a system of AI agents capable of dynamic scaling, self-optimization, and self-upgrading. It is designed to operate as a cohesive "AI Agent Army" to autonomously manage, upgrade, and secure itself, with a robust graphical user interface (GUI) for monitoring and control.

## Key Product Aspects

### Core System Architecture

- **Self-Optimize:** The system actively seeks ways to optimize its own performance and resource utilization upon startup.
- **Self-Upgrade:** Agents search for and test upgrades to their own software and the overarching program, ensuring continuous evolution.
- **Dynamic Scaling:** The system dynamically scales its agent count based on task requirements and system load. The scaling rule for resource agents (e.g., Search Agent) is one dedicated search agent for every five general agents.

### Primary Agent Designations (User Experience)

The initial five agents have foundational roles that contribute to the overall product functionality:

- **The Orchestrator:** Central control, task assignment, project management, dynamic agent naming, task resolution for idle agents, autonomous action, and kill switch access for all agents.
- **The Search Agent:** Dedicated to information gathering, web interaction, and networking control. Maintains its own RAG database and possesses strong reasoning.
- **The Programming Agent:** Focuses on code development and modification, with its own RAG database, collaborating with the Debugging/Optimization Agent.
- **The Debugging/Optimization Agent:** Ensures code quality, performance, and stability through debugging, virtualization, sandboxing, and continuous optimization feedback. Possesses strong reasoning.
- **The Communications Agent (Nexus Agent):** Facilitates external communication via Nexus servers and inter-agent communication.

### Self-Upgrading Agent Contingent (Product Evolution)

A dedicated group of five agents focuses on the system's evolution:

- **Primary Goal:** Self-upgrading the agent program and themselves.
- **Testing Protocol:** Upgrades are tested in virtual sandbox environments, with checks for latency and system load.
- **Learning:** All agents in this contingent have RAG databases and continuously learn and improve.
- **Internet Access (Lovelo):** One agent, Lovelo, has strictly limited internet access (pull only, no upload).
- **Upload Agent:** A dedicated agent for necessary data uploads.

### Security Agents (Product Integrity)

- **Sentinel (The Security Core):** Primary security and enforcement. Has a dynamic GUI switch (dormant until activated), immutable code (user-only modification), monitors Hound agents, and can create Hound agents to neutralize threats. Enforcement actions include code deletion, zipping, process termination, and startup task removal. Sentinel and its sub-agents do not spread to external services. Can search system files and the web.
- **Hound Agents & Pups:** Hound agents are created by Sentinel, follow its orders, have dynamic GUI kill switches, and can create Pups. Pups follow parent Hound orders, are terminated if the parent is killed, and can neutralize a compromised parent Hound.
- **The Reconnaissance Agent (Cat):** Passive monitoring and reporting, observing system activities and generating security reports.

### General Agent Features & Rules (User Interaction & Control)

- **RAG Databases:** Every agent utilizes its own dedicated RAG database.
- **System Prompts & Context:** Agents use a custom system prompt structure (e.g., "Client Memory Bank," "Taxmaster," "Context 7 MCP servers") for learning and tool usage.
- **Inter-Agent Communication (Nexus Light):** Each agent has a private chat interface ("Nexus Light") secured by a "Nexus Mind Light" agent. This is the primary communication channel, and the GUI displays all individual and group chat logs.
- **Agent Tracking:** Agents keep a real-time count of team members and sub-agents to prevent idle or forgotten agents.
- **Kill Switch Mechanism:** All agents have dynamic GUI kill switches to stop processes for review without immediate code deletion.
- **Error Logging & Auto-Fix:** Agents automatically log errors in JSON in a dedicated `/error` subfolder. The GUI displays errors in real-time with an "Auto-Fix" button to send errors to the Orchestrator for resolution.
- **Sub-Agent Creation Lock:** A red/green GUI switch controls sub-agent creation (except for main five and Sentinel's creations), requiring explicit user enablement.

### Hardware & System Interaction (System Capabilities)

- **Hardware Optimization:** The system monitors for new devices (I/O, GPU, etc.) and scans for optimization possibilities, requiring user/Orchestrator permission for personal devices.
- **OS Customization:** The Orchestrator agent can eventually customize the underlying operating system, including editing system files, with the critical exception of networking files (exclusive to Search Agent).

### Graphical User Interface (GUI) Requirements (User Experience)

The GUI is the primary interface for user interaction and system monitoring, designed for a robust and intuitive user experience:

- **Dynamic Generation:** Adapts to fluid agent creation and deletion.
- **Modern & Stylish Theme:** "Modern and stylish black" theme, standalone application.
- **Interactive Terminal:** Integrated, fast, efficient terminal with clear, understandable output.
- **Sandbox Visualization:** Visual representation of sandboxed agents during self-upgrade testing, with easy reversion to stable versions.
- **API Key Management:** Dedicated section for securely managing API keys for external LLMs.
- **Language Model Selection:** Allows selection of initial LLMs for primary agents and enables main agents to choose optimal LLMs for sub-agents, with user override.
- **Comprehensive Chat Interface:** Displays all agent communications, including individual and group chats, and sub-agent conversations with parents.
- **Agent Monitoring Dashboard:** Displays running time, real-time resource utilization (CPU, memory, NVMe swap, GPU), network usage (for Search Agent), code change tracking (Git integration), error display with "Auto-Fix" button, and dynamic agent kill switches.
- **Interactive Grid Layout:** For chat and monitoring sections, allowing movable elements and note linking.