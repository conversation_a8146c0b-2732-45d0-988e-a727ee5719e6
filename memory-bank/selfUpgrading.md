# AI Agent Army: Self-Upgrading Agent Contingent

This document details the dedicated group of five agents focused solely on the system's evolution, including their primary goals, testing protocols, learning mechanisms, and specialized roles.

## 3. Self-Upgrading Agent Contingent (Additional Five)

A dedicated group of five agents will focus solely on the system's evolution:

-   **Primary Goal:** Self-upgrading the agent program and themselves. This is the core mission of this contingent, ensuring the system's continuous improvement and adaptability.
-   **Testing Protocol:** These agents will test upgrades by cloning themselves into a virtual sandbox environment, running checks, analyzing latency, and assessing system load before implementation. This rigorous testing ensures stability and performance of new versions.
-   **Learning:** All agents in this contingent will have their own Retrieval Augmented Generation (RAG) databases and continuously learn and improve their own algorithms and performance. This fosters autonomous growth and optimization within the contingent.
-   **Internet Access (Lovelo):** One specific agent within this group, named <PERSON><PERSON>, will have strictly limited internet access. It can only pull down and scrape data and is never to upload anything to the internet under any circumstances. This is a critical security measure to prevent unauthorized data egress.
-   **Upload Agent:** Another agent within this contingent will be designated for any necessary data uploads, acting as a dedicated, controlled conduit for external data transmission. This centralizes and secures all outbound data transfers.

## 3.1. Integrating Darwin Gödel Machine (DGM) Principles

To achieve true self-improvement, the Self-Upgrading Agent Contingent will integrate principles from the Darwin Gödel Machine (DGM) research ([Zhang et al., 2025](https://arxiv.org/abs/2505.22954)). This involves:

-   **Iterative Self-Modification:** Agents will iteratively modify their own code and underlying algorithms, including those responsible for learning and improvement.
-   **Empirical Validation:** Proposed code changes will be empirically validated using comprehensive coding benchmarks (e.g., SWE-bench, Polyglot) within the virtual sandbox environment. Performance metrics will be rigorously analyzed to determine the efficacy of changes.
-   **Archive of Agents:** The system will maintain an archive of generated coding agents. This archive will grow by sampling existing agents and using foundation models to create new, improved versions.
-   **Open-Ended Exploration:** Inspired by Darwinian evolution, the system will employ open-ended exploration to discover diverse, high-quality agents. This will involve branching off from any agent in the growing archive, allowing for parallel exploration of different evolutionary paths and avoiding suboptimal designs.
-   **Tool and Workflow Improvements:** The self-upgrading process will focus on discovering and implementing improvements such as better code editing tools, enhanced file viewing, improved long-context window management, peer-review mechanisms, and generating/ranking multiple solutions.
-   **Safety and Transparency:** All self-modifications and evaluations will occur within secure, sandboxed environments under human supervision, with strict limits on web access. The agent archive will provide a transparent, traceable lineage of every change.