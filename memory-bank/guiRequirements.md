# AI Agent Army: Graphical User Interface (GUI) Requirements

This document details the comprehensive requirements for the Graphical User Interface (GUI) of the "AI Agent Army" system, which will serve as the primary interface for user interaction and system monitoring.

## 1. Overall GUI Philosophy & Theme
The GUI is designed to be highly dynamic, intuitive, and resource-aware, providing a comprehensive overview and granular control.

*   **Standalone Application:** The program will run as a native, self-contained desktop application. It will not require a web browser to operate, ensuring a smooth, dedicated experience.
*   **Modern & Stylish Black Theme:** The visual aesthetic will be sleek, modern, and predominantly black, but with carefully chosen accents and gradients to prevent it from feeling too stark.
*   **Color Palette:** Primarily deep, rich blacks and dark grays for backgrounds, with subtle luminous blues, greens, or purples used for highlights, interactive elements, text, and data visualizations. This creates a high-tech, futuristic feel.
*   **Typography:** Clean, sans-serif fonts for readability, with larger, bold typography for key metrics and agent names.
*   **Visual Cues:** Subtle animations, glowing effects for active components, and clear visual feedback for button presses and status changes.
*   **Dynamically Generated:** Crucially, the GUI layout and displayed elements (like agent monitoring cards, kill switches, chat feeds) will be dynamically generated. As agents are spawned or terminated, the GUI will automatically update to reflect the current operational structure. This supports the highly scalable nature of your agent army.

## 2. Core Layout Principles (Grid-Based & Modular)
The GUI will utilize a flexible, grid-like layout, allowing for a modular dashboard experience. This means:

*   **Movable Widgets:** Key information panels (e.g., System Metrics, Agent Tree, Chat Log, Error Feed) can potentially be repositioned or resized by the user within the main workspace.
*   **Linkable Notes/Elements:** Inspired by agent programming interfaces, elements could have "link" indicators, allowing the user to visually connect related agents, tasks, or data points (e.g., linking a bug report in the error log to the Debugging Agent responsible).
*   **Clear Sectioning:** Despite flexibility, core functionalities will be grouped into distinct, navigable sections or primary panes for ease of use.

## 3. Key GUI Sections & Features

### 3.1. Main Dashboard / System Overview Pane
This pane provides a high-level summary of the entire AI Agent Army's operational health.

*   **System-Wide Status:** A high-level summary of the entire AI Agent Army's operational health.
*   **Overall Resource Utilization:**
    *   CPU Usage: Real-time percentage display.
    *   Memory Usage: Current RAM consumption, potentially with a historical graph.
    *   NVMe Swap Space Utilization: Monitoring of the high-speed NVMe swap space, essential for managing many agents.
    *   GPU Utilization: Dynamic detection and display of any plugged-in GPUs, showing their current load and memory usage.
*   **Overall Network Usage:** Displays aggregate data transfer rates (in/out) for the entire system, particularly for internet-accessing agents.
*   **Total Agents Count:** A prominent display of the total number of active agents, including main agents and all sub-agents.
*   **Running Time (System Uptime):** Shows how long the entire program has been running.

### 3.2. Agent Management & Control Panel
This is where you directly interact with your agents.

*   **Agent Hierarchy View:** A clear, expandable tree-like or hierarchical list of all active agents. Main agents would be top-level, with their direct sub-agents nested beneath them, and Pups nested under Hounds.
*   **Individual Agent Status Cards/Rows:** For each agent listed:
    *   Dynamically Generated Name: Displays the unique name assigned by the Orchestrator.
    *   Role/Designation: Shows its primary function (e.g., Orchestrator, Search Agent, Hound, Pup).
    *   Running Time: How long this specific agent has been active.
    *   Individual Resource Metrics: Real-time CPU, memory, and (for Search Agent/sub-agents) network usage specific to that agent.
    *   Dynamic Kill Switch: A prominent red/green toggle switch (red for off/stopped, green for on/running). Clicking it will stop the agent's process, allowing for review without code deletion.
    *   LM Selection Override: A dropdown or button to override the default/Orchestrator-chosen Language Model for that specific agent or its sub-agents.
*   **Global Control Switches:**
    *   "Sub-Agent Creation" Lock: A large, prominent red/green toggle switch. When red (off), sub-agents (excluding those created by main agents or Sentinel) are prevented from creating further sub-agents.
    *   "Sentinel Activation" Switch: A dedicated red/green toggle switch to manually activate or deactivate the Sentinel agent.
    *   "Terminate All Hound Agents" Button: A prominent button to instantly shut off all currently active Hound agents, ensuring rapid control in case of a security overreaction.

### 3.3. Interactive Terminal
Integrated Console: A fully functional command-line interface directly within the GUI.

*   **Language Agnostic:** Designed to work efficiently regardless of the underlying programming language used by the agents.
*   **Understandable Output:** Agent output in the terminal will be highly verbose and user-friendly. It will include:
    *   Code Snippets with Explanations: When agents are working on code, the terminal will show relevant snippets along with quoted explanations of what the code is doing and why (e.g., "python print('Hello') # Orchestrator: This line prints a greeting to confirm basic functionality.").
    *   Narrative Descriptions: Agent actions, decisions, and progress will be described in clear, human-readable prose.
*   **User Input Line:** Allows direct commands to be sent to the Orchestrator or other designated agents.

### 3.4. Chat Interface (Nexus Light Hub)
Centralized Communication Log: A dedicated pane to monitor all inter-agent communications.

*   **Threaded/Tabbed Conversations:** Organize conversations by agent or by group.
*   **Main Agent Chats:** View direct conversations between the Orchestrator and other primary agents.
*   **Sub-Agent Chats:** Monitor communication between sub-agents and their parent agents (e.g., a Pup talking to its Hound, or a Programming Sub-Agent talking to the main Programming Agent).
*   **Group Chats:** Display dynamically created group chats formed by agents for collaborative tasks.
*   **Real-time Feed:** Messages appear in real-time, clearly indicating sender and recipient.
*   **Read-Only for User:** Primarily for monitoring, though direct input to the Orchestrator (or a specific agent via its control panel) is possible elsewhere.

### 3.5. Error Logging & Auto-Fix Panel
Live Error Feed: Displays error logs in real-time as they are written to the /error folder.

*   **JSON Formatting:** Errors are presented in an easily parsable JSON format, potentially with a user-friendly parsed view alongside it.
*   **"Auto-Fix" Button:** For any displayed error, a clickable button will appear. Pressing it will send the error details directly to the Orchestrator, instructing it to initiate a troubleshooting and fix process, potentially involving the Debugging/Optimization Agent.

### 3.6. Code Review & Sandbox Visualization
Sandbox Performance Display: For agents conducting self-upgrades or debugging, this section visually depicts the sandboxed environment. It shows:

*   Latency impact of the new code/upgrade.
*   Resource load (CPU/RAM) within the sandbox.
*   Success/failure metrics for tests.
*   Revert/Rollback Function: A prominent button to revert the system's code to the last stable version if a tested upgrade in the sandbox proves detrimental.
*   Git Integration: Visual display of code changes being tracked by Git. Allows for easy viewing of commit history, proposed changes, and a quick rollback to previous versions directly from the GUI.

### 3.7. Settings & Configuration Panel
API Key Management: A secure, encrypted interface for adding, editing, and deleting API keys for various external Language Models (e.g., Google Gemini, OpenRouter, LM Studios). Keys are stored securely and only accessed by authorized agents.

*   **Language Model Selection:**
    *   Dropdowns or lists to select the specific LLM that each of the primary five agents will use.
    *   Options to define default LLM choices for sub-agents, with the ability to override these defaults on an individual agent basis (as mentioned in Agent Management).
*   **Theme Customization:** Fine-grained controls for the "stylish black" theme, including accent color selectors, dark mode intensity sliders, and potentially font style/size options.
*   **Persistence Settings:** Options to configure how agent states, RAG databases, and project data are saved (e.g., auto-save frequency, manual save, backup locations).
*   **Log Configuration:** Adjustments for log verbosity (e.g., debug, info, warning, error), log file rotation settings, and retention policies.
*   **Notification Preferences:** Configure how the user receives alerts for critical events (e.g., security breaches, major errors, task completions, agent failures).

### 3.8. Task & Project Management View
Interactive Task Board: Potentially a Kanban-style board or a similar visual system where tasks delegated by the Orchestrator are displayed. Each task can show its current status, the agents assigned to it, and estimated progress.

*   **New Directive Input:** A dedicated area for the user to input new high-level directives or project goals for the Orchestrator to break down.
*   **Progress Monitoring:** Visual indicators of overall project progress and individual task completion.