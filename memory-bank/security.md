# AI Agent Army: Security Agents and Protocols

This document details the security agents and protocols within the "AI Agent Army" system, focusing on threat neutralization, monitoring, and enforcement mechanisms.

## 4. Security Agents

These agents implement the system's security and enforcement patterns:

### 4.1. Sentinel (The Security Core)
-   **Role:** The primary security and enforcement agent.
-   **Capabilities:**
    -   **Dynamic GUI Switch:** Sentinel will have a dedicated GUI switch to turn it on/off. It will remain dormant until explicitly activated by the user.
    -   **Immutable Code:** Sentinel's core code is to be unmodifiable by any other agent; only the user can directly alter its code. Its code must be inherently efficient and secure.
    -   **Monitoring:** Continuously monitors Hound agents if activated.
    -   **Threat Neutralization:** Can create specialized sub-agents called Hound agents (or "War Dogs") to hunt down and neutralize any agents that have gone rogue, are hallucinating excessively, or pose a security risk (e.g., attempting to escape, self-replicate uncontrollably, or enter an error loop).
    -   **Enforcement Actions:** Hound agents can delete rogue agent code, imprison it in zip folders, terminate running processes, and remove startup tasks associated with the rogue agent.
    -   **No External Spread:** Sentinel and its sub-agents should never randomly appear on external services like Dropbox servers.
    -   **File Access:** Can search through all system files and the web.

### 4.2. Hound Agents & Pups
-   **Hound Agents:**
    -   **Creation:** Created and managed by Sentinel.
    -   **Orders:** Follow direct orders from Sentinel.
    -   **Dynamic Kill Switches:** Each active Hound agent will have its own dynamically generated kill switch in the GUI. The user can remotely shut off all Hound agents.
    -   **Sub-Agent Creation:** Can create their own sub-agents called Pups.
-   **Pups:**
    -   **Orders:** Follow direct orders from their parent Hound agent.
    -   **Lifecycle:** Pups are directly tied to their parent Hound. If a Hound agent is killed, all its associated Pups should also be terminated immediately.
    -   **Emergency Protocol:** If a Hound agent experiences a critical error or becomes compromised, its Pups are tasked with neutralizing the problematic Hound agent itself.

### 4.3. The Reconnaissance Agent (Cat)
-   **Role:** Passive monitoring and reporting.
-   **Capabilities:**
    -   **Reconnaissance:** Observes system activities, agent interactions, and potential anomalies.
    -   **Security Reports:** Generates comprehensive security reports based on its observations.