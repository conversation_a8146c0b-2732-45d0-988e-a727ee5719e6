import time
import random
from agent_base import AgentBase

class ReconnaissanceAgent(AgentBase):
    """
    The Reconnaissance Agent (Cat) is responsible for passive monitoring and reporting.
    """
    def __init__(self, name: str, orchestrator):
        super().__init__(name=name, orchestrator=orchestrator)
        self.rag_database = self.rag_database # ReconnaissanceAgent has its own RAG database

    def observe_system_activities(self):
        """
        Simulates observing system activities and storing relevant data in the RAG database.
        Includes simulated logic for identifying suspicious patterns.
        """
        print(f"Reconnaissance Agent {self.name} observing system activities...")

        # Simulated system activity observation with suspicious pattern detection
        activity_data = {
            "timestamp": time.time(),
            "activity_type": "file_access",
            "details": f"User accessed /home/<USER>/documents/sensitive_data_{random.randint(1,100)}.txt"
        }

        # Simulate identifying suspicious patterns
        if "sensitive_data" in activity_data["details"]:
            print(f"Reconnaissance Agent {self.name} detected suspicious pattern in system activity: {activity_data['details']}")
            # Simulate logging the suspicious activity
            self.error_logger.log(f"ReconnaissanceAgent: Suspicious system activity detected - {activity_data['details']}")
            # Add to RAG database with a flag for suspicious activity
            activity_data["suspicious"] = True
        else:
            activity_data["suspicious"] = False

        self.rag_database.add_document("system_activity", str(activity_data))
        print(f"Observed system activity: {activity_data['activity_type']} (Suspicious: {activity_data['suspicious']})")
        return activity_data

    def observe_agent_interactions(self):
        """
        Simulates observing agent interactions and storing relevant data in the RAG database.
        Includes simulated logic for identifying suspicious patterns.
        """
        print(f"Reconnaissance Agent {self.name} observing agent interactions...")

        # Simulated agent interaction observation with suspicious pattern detection
        interaction_data = {
            "timestamp": time.time(),
            "interaction_type": "agent_communication",
            "details": f"Agent A communicated with Agent B about task {random.randint(1000,9999)}"
        }

        # Simulate identifying suspicious patterns
        suspicious_keywords = ["unauthorized", "escape", "self-replicate", "error loop"]
        if any(keyword in interaction_data["details"] for keyword in suspicious_keywords):
            print(f"Reconnaissance Agent {self.name} detected suspicious pattern in agent interaction: {interaction_data['details']}")
            # Simulate logging the suspicious activity
            self.error_logger.log(f"ReconnaissanceAgent: Suspicious agent interaction detected - {interaction_data['details']}")
            # Add to RAG database with a flag for suspicious activity
            interaction_data["suspicious"] = True
        else:
            interaction_data["suspicious"] = False

        self.rag_database.add_document("agent_interaction", str(interaction_data))
        print(f"Observed agent interaction: {interaction_data['interaction_type']} (Suspicious: {interaction_data['suspicious']})")
        return interaction_data

    def detect_anomalies(self):
        """
        Simulates detecting anomalies based on observed data and RAG database analysis.
        Includes enhanced logic for identifying suspicious patterns or anomalies.
        """
        print(f"Reconnaissance Agent {self.name} detecting anomalies...")

        # Retrieve observed data from RAG database
        retrieved_activities = self.rag_database.retrieve_information("system_activity")
        retrieved_interactions = self.rag_database.retrieve_information("agent_interaction")

        anomalies = []

        # Analyze system activities for anomalies
        for activity in retrieved_activities:
            activity_dict = eval(activity.content)  # Convert string to dict
            if activity_dict.get("suspicious"):
                anomalies.append(f"Suspicious system activity detected: {activity_dict['details']}")

        # Analyze agent interactions for anomalies
        for interaction in retrieved_interactions:
            interaction_dict = eval(interaction.content)  # Convert string to dict
            if interaction_dict.get("suspicious"):
                anomalies.append(f"Suspicious agent interaction detected: {interaction_dict['details']}")

        # Additional anomaly detection based on patterns
        if len(retrieved_activities) > 10 and random.random() > 0.8:  # Simulate anomaly with high activity
            anomalies.append("Unusually high number of file access attempts detected.")

        if len(retrieved_interactions) > 5 and random.random() > 0.7:  # Simulate anomaly with high interaction
            anomalies.append("Unusually high number of agent communications detected.")

        if anomalies:
            print(f"ANOMALY DETECTED: {anomalies}")
            self.rag_database.add_document("anomaly_alert", str(anomalies))
            # Notify SentinelAgent of detected anomalies
            self.nexus_light.send_message("SentinelAgent", {
                "type": "security_alert",
                "details": f"ReconnaissanceAgent detected anomalies: {anomalies}"
            })
        else:
            print("No anomalies detected.")
        return anomalies

    def generate_security_reports(self, observations: list):
        """
        Simulates generating comprehensive security reports based on observations and
        sending them to the Orchestrator via NexusLight. Ensures reports include
        details about simulated anomalies.
        """
        print(f"Reconnaissance Agent {self.name} generating security reports...")

        # Analyze observations for suspicious patterns
        suspicious_observations = []
        for observation in observations:
            if isinstance(observation, dict) and observation.get("suspicious"):
                suspicious_observations.append(observation)

        report_content = {
            "report_id": f"SEC-REP-{int(time.time())}",
            "timestamp": time.time(),
            "observations": observations,
            "suspicious_observations": suspicious_observations,
            "anomalies_detected": self.detect_anomalies(),
            "summary": "Simulated security report based on recent activities."
        }

        # Store the report in RAG database
        self.rag_database.add_document("security_report", str(report_content))
        print(f"Security report generated: {report_content['report_id']}")

        # Send report to Orchestrator via NexusLight
        self.nexus_light.send_message(
            recipient_agent_name="Orchestrator",
            message_payload={
                "type": "security_report",
                "sender": self.name,
                "report": report_content
            }
        )
        print("Security report sent to Orchestrator.")

    def run(self):
        """
        Main execution loop for the Reconnaissance Agent.
        Performs passive monitoring and reporting.
        """
        self.set_status("running")
        print(f"Reconnaissance Agent {self.name} started.")

        while self.is_active and not self._is_killed:
            try:
                self.set_status("monitoring")
                print(f"Reconnaissance Agent {self.name} performing passive monitoring...")
                
                # Perform observations
                system_activity = self.observe_system_activities()
                agent_interaction = self.observe_agent_interactions()
                
                # Collect observations for reporting
                current_observations = [system_activity, agent_interaction]

                # Detect anomalies
                self.detect_anomalies()

                # Generate and send reports periodically
                if random.random() > 0.5: # Simulate periodic reporting
                    self.generate_security_reports(current_observations)
                
                self.set_status("idle")
                time.sleep(random.uniform(5, 10)) # Simulate monitoring interval
                self.report_status_to_gui()
            except Exception as e:
                error_message = f"Unhandled exception in Reconnaissance Agent {self.name}: {e}"
                details = {"exception_type": type(e).__name__, "traceback": str(e)}
                self.error_logger.log_error(
                    agent_name=self.name,
                    error_message=error_message,
                    details=details
                )
                self.set_status("error")
                time.sleep(5)
        
        if self._is_killed:
            self.set_status("killed")
            print(f"Reconnaissance Agent {self.name} terminated.")
        elif not self.is_active:
            self.set_status("stopped")
            print(f"Reconnaissance Agent {self.name} gracefully stopped/paused.")