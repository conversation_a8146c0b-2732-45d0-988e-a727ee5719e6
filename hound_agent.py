import time
import random
from agent_base import AgentBase

class HoundAgent(AgentBase):
    """
    A specialized security agent created and managed by Sentinel, tasked with threat neutralization.
    """
    def __init__(self, name: str, orchestrator, sentinel_ref, is_sub_agent: bool = True):
        super().__init__(name=name, orchestrator=orchestrator, is_sub_agent=is_sub_agent)
        self.sentinel_ref = sentinel_ref
        self._kill_switch_active = False # Property for its dynamic kill switch
        # HoundAgents utilize the Context7MCPServer for context management
        if not self.context_7_mcp_server: # Ensure it's initialized if not by AgentBase
            self.context_7_mcp_server = Context7MCPServer()
        print(f"HoundAgent {self.name} initialized with Context7MCPServer for context.")


    @property
    def kill_switch_active(self):
        return self._kill_switch_active

    @kill_switch_active.setter
    def kill_switch_active(self, value: bool):
        self._kill_switch_active = value
        print(f"Hound Agent {self.name} kill switch active status set to: {self._kill_switch_active}")
        if self._kill_switch_active:
            print(f"Hound Agent {self.name} is now deactivated by kill switch.")
            self.stop() # Use the stop method from AgentBase to pause operation

    def follow_orders(self, order: dict):
        """
        Simulates executing orders from SentinelAgent with security checks.
        Ensures orders are valid and do not pose a risk to the system.
        """
        print(f"Hound Agent {self.name} received order from Sentinel: {order.get('description')}")
        self.set_status("executing_order")

        # Simulated security check: Validate order source and content
        if not self.validate_order_source(order) or not self.validate_order_content(order):
            print(f"Hound Agent {self.name} SECURITY ALERT - Invalid order received: {order}")
            self.error_logger.log(f"HoundAgent: Invalid order received from {order.get('source', 'unknown')}")
            # Notify SentinelAgent of potential issue
            self.nexus_light.send_message("SentinelAgent", {
                "type": "security_alert",
                "details": f"HoundAgent {self.name} received invalid order: {order}"
            })
            self.set_status("idle")
            return

        # Placeholder for actual order execution logic
        if order.get("type") == "neutralize_rogue":
            target = order.get("target")
            action = order.get("action")
            print(f"Hound Agent {self.name} is neutralizing {target} with action: {action}")

            # Execute the appropriate enforcement action with verification
            if action == "delete_code":
                self.delete_rogue_code(target)
            elif action == "imprison_zip":
                self.imprison_in_zip(target)
            elif action == "terminate_process":
                self.terminate_process(target)
            elif action == "remove_startup":
                self.remove_startup_task(target)
            else:
                print(f"Unknown neutralization action: {action}")

        # Simulate context interaction
        self.context_7_mcp_server.update_context(self.name, f"Executed order: {order.get('description')}")
        retrieved_context = self.context_7_mcp_server.get_context(self.name, "order execution")
        print(f"Hound Agent {self.name} context after order: {retrieved_context}")
        self.set_status("idle")

    def create_pup_agent(self, target_details: dict):
        """
        Simulates creating a PupAgent instance and assigning it a task.
        """
        print(f"Hound Agent {self.name} initiating creation of Pup Agent for: {target_details.get('name')}")
        task_description = f"Assist in neutralizing target: {target_details.get('name')}"
        
        # Call the Orchestrator's create_agent method
        # Assuming 'PupAgent' is the type string the Orchestrator expects
        # and 'self' is passed as the parent reference.
        self.orchestrator.create_agent(
            agent_type="PupAgent",
            name=f"Pup-{target_details.get('name')}-{random.randint(100,999)}",
            parent_agent=self,
            initial_task={"description": task_description, "target": target_details.get('name')}
        )
        print(f"Request sent to Orchestrator to create Pup Agent for {target_details.get('name')}.")

    def validate_order_source(self, order: dict) -> bool:
        """
        Simulates validating the source of an order to ensure it's from SentinelAgent.
        Returns True if the source is valid, False otherwise.
        """
        source = order.get('source', '')
        print(f"Hound Agent {self.name} validating order source: {source}")

        # Simulated validation logic
        # In a real scenario, this would involve cryptographic verification
        if source == "SentinelAgent":
            print(f"Hound Agent {self.name} confirmed valid order source: {source}")
            return True
        else:
            print(f"Hound Agent {self.name} detected invalid order source: {source}")
            return False

    def validate_order_content(self, order: dict) -> bool:
        """
        Simulates validating the content of an order to ensure it's valid.
        Returns True if the content is valid, False otherwise.
        """
        print(f"Hound Agent {self.name} validating order content: {order}")

        # Simulated validation logic
        # Check for required fields and basic sanity checks
        required_fields = ['type', 'target', 'action', 'description']
        if not all(field in order for field in required_fields):
            print(f"Hound Agent {self.name} detected missing fields in order")
            return False

        # Check for valid action types
        valid_actions = ['delete_code', 'imprison_zip', 'terminate_process', 'remove_startup']
        if order.get('action') not in valid_actions:
            print(f"Hound Agent {self.name} detected invalid action in order: {order.get('action')}")
            return False

        print(f"Hound Agent {self.name} confirmed valid order content")
        return True

    def delete_rogue_code(self, file_path: str):
        """
        Simulates deleting rogue agent code with verification.
        """
        # Simulated verification of the target
        if not self.verify_target_is_rogue(file_path, "code"):
            print(f"Hound Agent {self.name} SECURITY ALERT - Attempt to delete non-rogue code: {file_path}")
            self.error_logger.log(f"HoundAgent: Attempt to delete non-rogue code: {file_path}")
            # Notify SentinelAgent of potential issue
            self.nexus_light.send_message("SentinelAgent", {
                "type": "security_alert",
                "details": f"HoundAgent {self.name} attempted to delete non-rogue code: {file_path}"
            })
            return

        print(f"SIMULATING: Deleting rogue code at {file_path}")
        # Placeholder for actual file deletion logic
        time.sleep(random.uniform(0.5, 1.5))
        print(f"SIMULATED: Code at {file_path} deleted.")

    def imprison_in_zip(self, file_path: str):
        """
        Simulates imprisoning rogue agent code in zip folders with verification.
        """
        # Simulated verification of the target
        if not self.verify_target_is_rogue(file_path, "code"):
            print(f"Hound Agent {self.name} SECURITY ALERT - Attempt to imprison non-rogue code: {file_path}")
            self.error_logger.log(f"HoundAgent: Attempt to imprison non-rogue code: {file_path}")
            # Notify SentinelAgent of potential issue
            self.nexus_light.send_message("SentinelAgent", {
                "type": "security_alert",
                "details": f"HoundAgent {self.name} attempted to imprison non-rogue code: {file_path}"
            })
            return

        print(f"SIMULATING: Imprisoning code at {file_path} in a zip folder.")
        # Placeholder for actual zipping logic
        time.sleep(random.uniform(1, 2))
        print(f"SIMULATED: Code at {file_path} imprisoned in zip.")

    def terminate_process(self, process_id: str):
        """
        Simulates terminating running processes of rogue agents with verification.
        """
        # Simulated verification of the target
        if not self.verify_target_is_rogue(process_id, "process"):
            print(f"Hound Agent {self.name} SECURITY ALERT - Attempt to terminate non-rogue process: {process_id}")
            self.error_logger.log(f"HoundAgent: Attempt to terminate non-rogue process: {process_id}")
            # Notify SentinelAgent of potential issue
            self.nexus_light.send_message("SentinelAgent", {
                "type": "security_alert",
                "details": f"HoundAgent {self.name} attempted to terminate non-rogue process: {process_id}"
            })
            return

        print(f"SIMULATING: Terminating process with ID: {process_id}")
        # Placeholder for actual process termination logic
        time.sleep(random.uniform(0.5, 1.0))
        print(f"SIMULATED: Process {process_id} terminated.")

    def remove_startup_task(self, task_name: str):
        """
        Simulates removing startup tasks associated with rogue agents with verification.
        """
        # Simulated verification of the target
        if not self.verify_target_is_rogue(task_name, "startup_task"):
            print(f"Hound Agent {self.name} SECURITY ALERT - Attempt to remove non-rogue startup task: {task_name}")
            self.error_logger.log(f"HoundAgent: Attempt to remove non-rogue startup task: {task_name}")
            # Notify SentinelAgent of potential issue
            self.nexus_light.send_message("SentinelAgent", {
                "type": "security_alert",
                "details": f"HoundAgent {self.name} attempted to remove non-rogue startup task: {task_name}"
            })
            return

        print(f"SIMULATING: Removing startup task: {task_name}")
        # Placeholder for actual startup task removal logic
        time.sleep(random.uniform(0.5, 1.0))
        print(f"SIMULATED: Startup task {task_name} removed.")

    def verify_target_is_rogue(self, target: str, target_type: str) -> bool:
        """
        Simulates verifying that a target (file, process, task) is indeed rogue before taking action.
        Returns True if the target is confirmed rogue, False otherwise.
        """
        print(f"Hound Agent {self.name} verifying {target_type} target: {target} is rogue...")

        # Simulated verification logic
        # In a real scenario, this would involve checking against a list of known rogue agents
        # or performing other security checks
        known_rogue_indicators = ['rogue', 'malicious', 'unauthorized']

        if any(indicator in target.lower() for indicator in known_rogue_indicators):
            print(f"Hound Agent {self.name} confirmed {target_type} target {target} is rogue.")
            return True
        elif target_type == "code" and target.endswith('.py'):
            # Special case for Python files - simulate deeper inspection
            if random.random() > 0.8:  # Simulate occasional false positive
                print(f"Hound Agent {self.name} detected suspicious activity in {target}. Marked as rogue.")
                return True
        elif target_type == "process" and target.isdigit():
            # Special case for process IDs - simulate checking process list
            if random.random() > 0.7:  # Simulate occasional match
                print(f"Hound Agent {self.name} found process ID {target} in rogue process list.")
                return True

        print(f"Hound Agent {self.name} could not confirm {target_type} target {target} is rogue.")
        return False

    def run(self):
        """
        Main execution loop for the Hound Agent.
        Respects the dynamic kill switch.
        """
        self.set_status("running")
        print(f"Hound Agent {self.name} started.")

        while self.is_active and not self._is_killed and not self.kill_switch_active:
            try:
                self.set_status("idle")
                print(f"Hound Agent {self.name} is idle, waiting for orders from Sentinel or Orchestrator...")
                
                # Simulate receiving orders or tasks
                # In a real scenario, this would involve listening to NexusLight messages
                # or querying the Orchestrator for specific directives.
                response = self.nexus_light.query_orchestrator_for_directives()
                
                if response and response.get("type") == "task_assigned":
                    task = response.get("task")
                    self.current_task = task
                    self.follow_orders(task) # Hound agents follow orders
                    
                    # Report task completion
                    self.nexus_light.send_message(
                        recipient_agent_name="Orchestrator",
                        message_payload={
                            "type": "task_completed",
                            "task_id": task["task_id"],
                            "status": "completed",
                            "content": f"Task '{task['description']}' completed by Hound Agent {self.name}."
                        }
                    )
                    self.set_status("completed_task")
                    self.current_task = None
                elif response and response.get("type") == "no_task_available":
                    print(f"Hound Agent {self.name}: {response.get('content')}")
                    self.set_status("idle")
                    time.sleep(5)
                else:
                    print(f"Hound Agent {self.name}: No valid response from Orchestrator or no task assigned. Waiting...")
                    self.set_status("idle")
                    time.sleep(5)

                self.report_status_to_gui()
            except Exception as e:
                error_message = f"Unhandled exception in Hound Agent {self.name}: {e}"
                details = {"exception_type": type(e).__name__, "traceback": str(e)}
                self.error_logger.log_error(
                    agent_name=self.name,
                    error_message=error_message,
                    details=details
                )
                self.set_status("error")
                time.sleep(5)
        
        if self._is_killed:
            self.set_status("killed")
            print(f"Hound Agent {self.name} terminated due to kill switch or parent termination.")
        elif not self.is_active or self.kill_switch_active:
            self.set_status("stopped")
            print(f"Hound Agent {self.name} gracefully stopped/paused.")